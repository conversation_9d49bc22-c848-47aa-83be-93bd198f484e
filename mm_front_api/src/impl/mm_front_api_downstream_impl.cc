/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 * 
 * Author:  Febao
 * Date:    2025-07-28 10:49:05
 * Version: 0.0.7
 */

#include <cffex/log/xlog.h>

#include "mm_front_api_impl.h"
#include "mm_front_api_msg_impl.h"
#include "mm_msgid.h"
#include "mm_id_manager.h"

using namespace cffex::mm; 

void mm_front_api_impl::on_connected(const char* ip) {
    spi_->on_front_connected(ip);
}

void mm_front_api_impl::on_disconnected(const char* ip) {
    spi_->on_front_disconnected(ip);
}

void mm_front_api_impl::on_rsp_info(int error_id, const char *error_msg, int request_id) {
    rsp_info_field f;
    f.error_id  = error_id;
    f.error_msg = error_msg;
    rsp_info_msg_impl impl(&f);
    spi_->on_rsp_info(&impl, request_id);
}

void mm_front_api_impl::on_rtn_market_data(market_data_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    market_data_msg_impl impl((market_data_field *)r);
    spi_->on_rtn_market_data(&impl);
}

void mm_front_api_impl::on_rtn_order(order_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    order_msg_impl impl((order_field *)r);
    spi_->on_rtn_order(&impl);
}

void mm_front_api_impl::on_rtn_security_instrument(security_instrument_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    security_instrument_msg_impl impl((security_instrument_field *)r);
    spi_->on_rtn_security_instrument(&impl);
}

void mm_front_api_impl::on_rtn_option_instrument(option_instrument_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    option_instrument_msg_impl impl((option_instrument_field *)r);
    spi_->on_rtn_option_instrument(&impl);
}

void mm_front_api_impl::on_rtn_future_instrument(future_instrument_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    future_instrument_msg_impl impl((future_instrument_field *)r);
    spi_->on_rtn_future_instrument(&impl);
}

void mm_front_api_impl::on_rtn_position(position_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    position_msg_impl impl((position_field *)r);
    spi_->on_rtn_position(&impl);
}

void mm_front_api_impl::on_rtn_instrument_param_define(instrument_param_define_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    instrument_param_define_msg_impl impl((instrument_param_define_field *)r);
    spi_->on_rtn_instrument_param_define(&impl);
}

void mm_front_api_impl::on_rtn_instrument_param_value(instrument_param_value_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    instrument_param_value_msg_impl impl((instrument_param_value_field *)r);
    spi_->on_rtn_instrument_param_value(&impl);
}

void mm_front_api_impl::on_rtn_custom_param_define(custom_param_define_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    custom_param_define_msg_impl impl((custom_param_define_field *)r);
    spi_->on_rtn_custom_param_define(&impl);
}

void mm_front_api_impl::on_rtn_custom_param_value(custom_param_value_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    custom_param_value_msg_impl impl((custom_param_value_field *)r);
    spi_->on_rtn_custom_param_value(&impl);
}

void mm_front_api_impl::on_rtn_user(user_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    user_msg_impl impl((user_field *)r);
    spi_->on_rtn_user(&impl);
}

void mm_front_api_impl::on_rtn_user_password(user_password_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    user_password_msg_impl impl((user_password_field *)r);
    spi_->on_rtn_user_password(&impl);
}

void mm_front_api_impl::on_rtn_trading_account(trading_account_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    trading_account_msg_impl impl((trading_account_field *)r);
    spi_->on_rtn_trading_account(&impl);
}

void mm_front_api_impl::on_rtn_trader_account_config(trader_account_config_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    trader_account_config_msg_impl impl((trader_account_config_field *)r);
    spi_->on_rtn_trader_account_config(&impl);
}

void mm_front_api_impl::on_rtn_account_product_config(account_product_config_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    account_product_config_msg_impl impl((account_product_config_field *)r);
    spi_->on_rtn_account_product_config(&impl);
}

void mm_front_api_impl::on_rtn_product(product_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    product_msg_impl impl((product_field *)r);
    spi_->on_rtn_product(&impl);
}

void mm_front_api_impl::on_rtn_notify_cancel_order(notify_cancel_order_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    notify_cancel_order_msg_impl impl((notify_cancel_order_field *)r);
    spi_->on_rtn_notify_cancel_order(&impl);
}

void mm_front_api_impl::on_rtn_create_redempt_order(create_redempt_order_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    create_redempt_order_msg_impl impl((create_redempt_order_field *)r);
    spi_->on_rtn_create_redempt_order(&impl);
}

void mm_front_api_impl::on_rtn_trade(trade_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    trade_msg_impl impl((trade_field *)r);
    spi_->on_rtn_trade(&impl);
}

void mm_front_api_impl::on_rtn_investor_account_fund(investor_account_fund_record *r) {
    GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, [%s]\n", __FUNCTION__, r->to_string().c_str());
    investor_account_fund_msg_impl impl((investor_account_fund_field *)r);
    spi_->on_rtn_investor_account_fund(&impl);
}

void mm_front_api_impl::do_init() {
    adapter_.init_table_rebuilder<market_data_table>(MSG_PUB_MD, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("market_data_snap", [this]() {
        return puber_manager_
            .register_puber<market_data_snap_puber, market_data_table, market_data_snap_func_type>(
                mdb_.get_table<market_data_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_market_data, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("market_data_table", [this]() {
        return puber_manager_
            .register_puber<market_data_table_puber, market_data_table, market_data_table_func_type>(
                mdb_.get_table<market_data_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_market_data, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<order_table>(MSG_QUERY_RTN_ORDER, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("order_snap", [this]() {
        return puber_manager_
            .register_puber<order_snap_puber, order_table, order_snap_func_type>(
                mdb_.get_table<order_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_order, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("order_table", [this]() {
        return puber_manager_
            .register_puber<order_table_puber, order_table, order_table_func_type>(
                mdb_.get_table<order_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_order, this, cffex::_1));
    }));
    adapter_.init_custom_rebuilder<instrument_table, security_instrument_field>(MSG_INIT_SECURITY_INSTRUMENT, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("security_instrument_snap", [this]() {
        return puber_manager_
            .register_puber<security_instrument_snap_puber, security_instrument_table, security_instrument_snap_func_type>(
                mdb_.get_table<security_instrument_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_security_instrument, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("security_instrument_table", [this]() {
        return puber_manager_
            .register_puber<security_instrument_table_puber, security_instrument_table, security_instrument_table_func_type>(
                mdb_.get_table<security_instrument_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_security_instrument, this, cffex::_1));
    }));
    adapter_.init_custom_rebuilder<instrument_table, option_instrument_field>(MSG_INIT_OPTION_INSTRUMENT, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("option_instrument_snap", [this]() {
        return puber_manager_
            .register_puber<option_instrument_snap_puber, option_instrument_table, option_instrument_snap_func_type>(
                mdb_.get_table<option_instrument_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_option_instrument, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("option_instrument_table", [this]() {
        return puber_manager_
            .register_puber<option_instrument_table_puber, option_instrument_table, option_instrument_table_func_type>(
                mdb_.get_table<option_instrument_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_option_instrument, this, cffex::_1));
    }));
    adapter_.init_custom_rebuilder<instrument_table, future_instrument_field>(MSG_INIT_FUTURE_INSTRUMENT, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("future_instrument_snap", [this]() {
        return puber_manager_
            .register_puber<future_instrument_snap_puber, future_instrument_table, future_instrument_snap_func_type>(
                mdb_.get_table<future_instrument_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_future_instrument, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("future_instrument_table", [this]() {
        return puber_manager_
            .register_puber<future_instrument_table_puber, future_instrument_table, future_instrument_table_func_type>(
                mdb_.get_table<future_instrument_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_future_instrument, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<position_table>(MSG_QUERY_RTN_POSITION, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("position_snap", [this]() {
        return puber_manager_
            .register_puber<position_snap_puber, position_table, position_snap_func_type>(
                mdb_.get_table<position_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_position, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("position_table", [this]() {
        return puber_manager_
            .register_puber<position_table_puber, position_table, position_table_func_type>(
                mdb_.get_table<position_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_position, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<instrument_param_define_table>(MSG_INIT_INSTRUMENT_PARAM_DEFINE, &mdb_, subscribe_info_);
    adapter_.init_table_rebuilder<instrument_param_define_table>(MSG_ADD_OR_UPDATE_INSTRUMENT_PARAM_DEFINE, &mdb_, subscribe_info_);
    adapter_.init_table_rebuilder<instrument_param_define_table>(MSG_MODIFY_INSTRUMENT_PARAM_DEFINE, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("instrument_param_define_snap", [this]() {
        return puber_manager_
            .register_puber<instrument_param_define_snap_puber, instrument_param_define_table, instrument_param_define_snap_func_type>(
                mdb_.get_table<instrument_param_define_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_instrument_param_define, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("instrument_param_define_table", [this]() {
        return puber_manager_
            .register_puber<instrument_param_define_table_puber, instrument_param_define_table, instrument_param_define_table_func_type>(
                mdb_.get_table<instrument_param_define_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_instrument_param_define, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<instrument_param_value_table>(MSG_INIT_INSTRUMENT_PARAM_VALUE, &mdb_, subscribe_info_);
    adapter_.init_table_rebuilder<instrument_param_value_table>(MSG_ADD_OR_UPDATE_INSTRUMENT_PARAM_VALUE, &mdb_, subscribe_info_);
    adapter_.init_table_rebuilder<instrument_param_value_table>(MSG_MODIFY_INSTRUMENT_PARAM_VALUE, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("instrument_param_value_snap", [this]() {
        return puber_manager_
            .register_puber<instrument_param_value_snap_puber, instrument_param_value_table, instrument_param_value_snap_func_type>(
                mdb_.get_table<instrument_param_value_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_instrument_param_value, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("instrument_param_value_table", [this]() {
        return puber_manager_
            .register_puber<instrument_param_value_table_puber, instrument_param_value_table, instrument_param_value_table_func_type>(
                mdb_.get_table<instrument_param_value_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_instrument_param_value, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<custom_param_define_table>(MSG_INIT_CUSTOM_PARAM_DEFINE, &mdb_, subscribe_info_);
    adapter_.init_table_rebuilder<custom_param_define_table>(MSG_ADD_OR_UPDATE_CUSTOM_PARAM_DEFINE, &mdb_, subscribe_info_);
    adapter_.init_table_rebuilder<custom_param_define_table>(MSG_MODIFY_CUSTOM_PARAM_DEFINE, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("custom_param_define_snap", [this]() {
        return puber_manager_
            .register_puber<custom_param_define_snap_puber, custom_param_define_table, custom_param_define_snap_func_type>(
                mdb_.get_table<custom_param_define_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_custom_param_define, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("custom_param_define_table", [this]() {
        return puber_manager_
            .register_puber<custom_param_define_table_puber, custom_param_define_table, custom_param_define_table_func_type>(
                mdb_.get_table<custom_param_define_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_custom_param_define, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<custom_param_value_table>(MSG_INIT_CUSTOM_PARAM_VALUE, &mdb_, subscribe_info_);
    adapter_.init_table_rebuilder<custom_param_value_table>(MSG_ADD_OR_UPDATE_CUSTOM_PARAM_VALUE, &mdb_, subscribe_info_);
    adapter_.init_table_rebuilder<custom_param_value_table>(MSG_MODIFY_CUSTOM_PARAM_VALUE, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("custom_param_value_snap", [this]() {
        return puber_manager_
            .register_puber<custom_param_value_snap_puber, custom_param_value_table, custom_param_value_snap_func_type>(
                mdb_.get_table<custom_param_value_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_custom_param_value, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("custom_param_value_table", [this]() {
        return puber_manager_
            .register_puber<custom_param_value_table_puber, custom_param_value_table, custom_param_value_table_func_type>(
                mdb_.get_table<custom_param_value_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_custom_param_value, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<user_table>(MSG_INIT_USER, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("user_snap", [this]() {
        return puber_manager_
            .register_puber<user_snap_puber, user_table, user_snap_func_type>(
                mdb_.get_table<user_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_user, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("user_table", [this]() {
        return puber_manager_
            .register_puber<user_table_puber, user_table, user_table_func_type>(
                mdb_.get_table<user_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_user, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<user_password_table>(MSG_INIT_USER_PASSWORD, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("user_password_snap", [this]() {
        return puber_manager_
            .register_puber<user_password_snap_puber, user_password_table, user_password_snap_func_type>(
                mdb_.get_table<user_password_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_user_password, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("user_password_table", [this]() {
        return puber_manager_
            .register_puber<user_password_table_puber, user_password_table, user_password_table_func_type>(
                mdb_.get_table<user_password_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_user_password, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<trading_account_table>(MSG_INIT_TRADING_ACCOUNT, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("trading_account_snap", [this]() {
        return puber_manager_
            .register_puber<trading_account_snap_puber, trading_account_table, trading_account_snap_func_type>(
                mdb_.get_table<trading_account_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_trading_account, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("trading_account_table", [this]() {
        return puber_manager_
            .register_puber<trading_account_table_puber, trading_account_table, trading_account_table_func_type>(
                mdb_.get_table<trading_account_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_trading_account, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<trader_account_config_table>(MSG_INIT_TRADER_ACCOUNT_CONFIG, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("trader_account_config_snap", [this]() {
        return puber_manager_
            .register_puber<trader_account_config_snap_puber, trader_account_config_table, trader_account_config_snap_func_type>(
                mdb_.get_table<trader_account_config_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_trader_account_config, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("trader_account_config_table", [this]() {
        return puber_manager_
            .register_puber<trader_account_config_table_puber, trader_account_config_table, trader_account_config_table_func_type>(
                mdb_.get_table<trader_account_config_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_trader_account_config, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<account_product_config_table>(MSG_INIT_ACCOUNT_PRODUCT_CONFIG, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("account_product_config_snap", [this]() {
        return puber_manager_
            .register_puber<account_product_config_snap_puber, account_product_config_table, account_product_config_snap_func_type>(
                mdb_.get_table<account_product_config_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_account_product_config, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("account_product_config_table", [this]() {
        return puber_manager_
            .register_puber<account_product_config_table_puber, account_product_config_table, account_product_config_table_func_type>(
                mdb_.get_table<account_product_config_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_account_product_config, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<product_table>(MSG_INIT_PRODUCT, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("product_snap", [this]() {
        return puber_manager_
            .register_puber<product_snap_puber, product_table, product_snap_func_type>(
                mdb_.get_table<product_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_product, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("product_table", [this]() {
        return puber_manager_
            .register_puber<product_table_puber, product_table, product_table_func_type>(
                mdb_.get_table<product_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_product, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<notify_cancel_order_table>(MSG_NTF_CANCEL_ORDER, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("notify_cancel_order_snap", [this]() {
        return puber_manager_
            .register_puber<notify_cancel_order_snap_puber, notify_cancel_order_table, notify_cancel_order_snap_func_type>(
                mdb_.get_table<notify_cancel_order_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_notify_cancel_order, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("notify_cancel_order_table", [this]() {
        return puber_manager_
            .register_puber<notify_cancel_order_table_puber, notify_cancel_order_table, notify_cancel_order_table_func_type>(
                mdb_.get_table<notify_cancel_order_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_notify_cancel_order, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<create_redempt_order_table>(MSG_QUERY_RTN_CREATE_REDEMPT_ORDER, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("create_redempt_order_snap", [this]() {
        return puber_manager_
            .register_puber<create_redempt_order_snap_puber, create_redempt_order_table, create_redempt_order_snap_func_type>(
                mdb_.get_table<create_redempt_order_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_create_redempt_order, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("create_redempt_order_table", [this]() {
        return puber_manager_
            .register_puber<create_redempt_order_table_puber, create_redempt_order_table, create_redempt_order_table_func_type>(
                mdb_.get_table<create_redempt_order_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_create_redempt_order, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<trade_table>(MSG_QUERY_RTN_TRADE, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("trade_snap", [this]() {
        return puber_manager_
            .register_puber<trade_snap_puber, trade_table, trade_snap_func_type>(
                mdb_.get_table<trade_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_trade, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("trade_table", [this]() {
        return puber_manager_
            .register_puber<trade_table_puber, trade_table, trade_table_func_type>(
                mdb_.get_table<trade_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_trade, this, cffex::_1));
    }));
    adapter_.init_table_rebuilder<investor_account_fund_table>(MSG_RTN_ACCOUNT_FUND, &mdb_, subscribe_info_);
    puber_funcs_.insert(std::make_pair("investor_account_fund_snap", [this]() {
        return puber_manager_
            .register_puber<investor_account_fund_snap_puber, investor_account_fund_table, investor_account_fund_snap_func_type>(
                mdb_.get_table<investor_account_fund_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_investor_account_fund, this, cffex::_1));
    }));
    puber_funcs_.insert(std::make_pair("investor_account_fund_table", [this]() {
        return puber_manager_
            .register_puber<investor_account_fund_table_puber, investor_account_fund_table, investor_account_fund_table_func_type>(
                mdb_.get_table<investor_account_fund_table>(),
                cffex::bind(&mm_front_api_impl::on_rtn_investor_account_fund, this, cffex::_1));
    }));
}
