/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 * 
 * Author:  Febao
 * Date:    2025-07-28 10:49:05
 * Version: 0.0.7
 */

#ifndef CFFEX_MM_FRONT_API_IMPL_H
#define CFFEX_MM_FRONT_API_IMPL_H


#include "mm_front_api.h"
#include "mm_front_adapter.h"
#include "mm_front_api_limiter.h"
#include "mm_front_api_mdb.h"
#include "mm_front_api_puber_manager.h"
#include "mm_front_api_entity_creator_impl.h"
#include "mm_table.h"
#include <cffex/biz/biz_parser.h>
#include <cffex/system/error_code.h>
#include <cffex/net.h>
#include <set>

using namespace cffex::mm; 

class subscribe_info_impl : public front_api::mm_front_api::subscribe_info {
public:
    subscribe_info_impl() = default;
    virtual ~subscribe_info_impl() = default;
    virtual void append(const char *table, int interval) override;
    virtual bool first(char table[64], int &interval) override;
    virtual bool next(char table[64], int &interval) override;

    virtual void set_subscribe_instruments(const std::vector<std::string> &instruments) override;
    virtual bool is_instrument_subscribed(const char *instrument_id) override;

    virtual void set_enable_instrument_filter(bool enable) override;
    virtual bool is_instrument_filter_enabled() override;

private:
    struct sub_detail {
        std::string name;
        int         interval;
    };
    std::map<std::string, sub_detail>            sub_info_map_;
    std::map<std::string, sub_detail>::iterator  sub_info_map_itor_;
    std::set<std::string>                        subscribe_instruments_;
    bool                                         enable_instrument_filter_;
};

class mm_front_api_impl : public front_api::mm_front_api {
public:
    mm_front_api_impl(unsigned short node_id, const char *log_level, const char *log_dir);
    virtual ~mm_front_api_impl();
    virtual void release() override;
    virtual void init(const char *pub_addr, const char *req_addr, subscribe_info *sub_info) override;
    virtual void start() override;
    virtual int  join() override;
    virtual void register_spi(front_api::mm_front_spi *spi) override;

    virtual front_api::mm_front_api_entity_creator *get_entity_creator() override { return &entity_creator_; }
    virtual int  insert_order(front_api::order_entity *order, int request_id) override;
    virtual int  cancel_order(front_api::order_entity *order, int request_id) override;
    virtual int  modify_instrument_param_value(front_api::instrument_param_value_entity *instrument_param_value, int request_id) override;
    virtual int  modify_custom_param_value(front_api::custom_param_value_entity *custom_param_value, int request_id) override;

protected:
    void on_connected(const char* ip);
    void on_disconnected(const char* ip);
    void on_rsp_info(int error_id, const char *error_msg, int request_id);
    void on_rtn_market_data(market_data_record *r);
    void on_rtn_order(order_record *r);
    void on_rtn_security_instrument(security_instrument_record *r);
    void on_rtn_option_instrument(option_instrument_record *r);
    void on_rtn_future_instrument(future_instrument_record *r);
    void on_rtn_position(position_record *r);
    void on_rtn_instrument_param_define(instrument_param_define_record *r);
    void on_rtn_instrument_param_value(instrument_param_value_record *r);
    void on_rtn_custom_param_define(custom_param_define_record *r);
    void on_rtn_custom_param_value(custom_param_value_record *r);
    void on_rtn_user(user_record *r);
    void on_rtn_user_password(user_password_record *r);
    void on_rtn_trading_account(trading_account_record *r);
    void on_rtn_trader_account_config(trader_account_config_record *r);
    void on_rtn_account_product_config(account_product_config_record *r);
    void on_rtn_product(product_record *r);
    void on_rtn_notify_cancel_order(notify_cancel_order_record *r);
    void on_rtn_create_redempt_order(create_redempt_order_record *r);
    void on_rtn_trade(trade_record *r);
    void on_rtn_investor_account_fund(investor_account_fund_record *r);

private:
    void do_init();
private:
    template<typename RECORD, typename FIELD, typename MSG_IMPL, typename SPI_FUNC>
    void process_rtn_with_instrument_filter(RECORD *r, SPI_FUNC spi_func, const char* func_name);
private:
    template<typename FIELD>
    int  send_field(uint16_t msgid, const FIELD &f, uint32_t request_id) {
        en_.init(msgid);
        instrument_record* r = mdb_.get_table<instrument_table>()->get(f.instrument_id);
        if (r != NULL) {
            GXLOG(XLOG_DEBUG, "mm_front_api_impl::%s, find instrument_id[%s]\n", __FUNCTION__, f.instrument_id.get_value());
            en_.set_ex_header(r->product_id.get_value(), strlen(r->product_id.get_value()));
        }
        if (r == NULL) {
            GXLOG(XLOG_WARNING, "mm_front_api_impl::%s, cannnot find instrument_id[%s]\n", __FUNCTION__, f.instrument_id.get_value());
        }
        en_.set_field((typename FIELD::field_type &)f);
        en_.set_nodeid(0);
        return adapter_.send_req(en_, request_id);
    }

    template<typename FIELD>
    int  send_param_field(uint16_t msgid, const FIELD &f, uint32_t request_id) {
        en_.init(msgid);
        en_.set_field((typename FIELD::field_type &)f);
        en_.set_nodeid(0);
        return adapter_.send_req(en_, request_id);
    }

private:
    unsigned short                                                  node_id_;
    mm_front_adapter                                                adapter_;
    front_api::mm_front_spi                                        *spi_{nullptr};
    mm_front_api_limiter                                            limiter_;
    mm_front_api_mdb                                                mdb_;
    mm_front_api_puber_manager                                      puber_manager_;
    mm_front_api_entity_creator_impl                                entity_creator_;
    cffex::biz::biz_encoder                                         en_;
    std::map<std::string, std::function<i_mm_front_api_puber *()>>  puber_funcs_;
    front_api::mm_front_api::subscribe_info                         *subscribe_info_{nullptr};
};

#endif
