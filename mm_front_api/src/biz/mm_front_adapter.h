/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: jinfy
 * Date: 2023-09-12
 */

#ifndef CFFEX_MM_FRONT_ADAPTER_H
#define CFFEX_MM_FRONT_ADAPTER_H

#include <cffex/biz/biz_client.h>
#include <cffex/event.h>
#include <cffex/net.h>
#include <cffex/system/error_code.h>
#include <cstdint>

#include "mm_front_api_mdb.h"
#include "mm_front_api_rebuilder.h"
#include "mm_msgid.h"

namespace cffex {
namespace mm {

using net_callback_type   = std::function<void(const char *)>;
using rsp_callback_type   = std::function<void(const cffex::biz_decoder *, uint32_t, bool)>;
using error_callback_type = std::function<void(int, const char *, int)>;

class mm_front_adapter {
public:
    mm_front_adapter();
    virtual ~mm_front_adapter();
    void init(const char *pub_addr, const char *req_addr);

    void                      start();
    void                      stop();
    void                      join();
    cffex::io_service_thread *get_service_thread();

    void set_connected_callback(const net_callback_type &cb);
    void set_disconnected_callback(const net_callback_type &cb);
    void set_error_callback(const error_callback_type &cb);
    void insert_rsp_callback(uint32_t msgid, const rsp_callback_type &cb);

    int send_req(cffex::biz::biz_encoder &en, uint32_t request_id);

    template<typename TABLE, typename FIELD>
    void init_custom_rebuilder(uint32_t msgid, mm_front_api_mdb *mdb, front_api::mm_front_api::subscribe_info *sub_info =nullptr) {
        XLOG(XLOG_DEBUG, "mm_front_adapter::%s, msgid[0x%x]\n", __FUNCTION__, msgid);
        if (msgid == MSG_INIT_SECURITY_INSTRUMENT) {
            XLOG(XLOG_DEBUG, "mm_front_adapter::%s, register security_instrument_table\n");
            mdb->register_table<security_instrument_table>();
        };
        if (msgid == MSG_INIT_FUTURE_INSTRUMENT) {
            XLOG(XLOG_DEBUG, "mm_front_adapter::%s, register future_instrument_table\n");
            mdb->register_table<future_instrument_table>();
        };
        if (msgid == MSG_INIT_OPTION_INSTRUMENT) {
            XLOG(XLOG_DEBUG, "mm_front_adapter::%s, register option_instrument_table\n");
            mdb->register_table<option_instrument_table>();
        };
        mdb->register_table<TABLE>();
        rebuilders_[msgid] = new mm_front_api_custom_rebuilder<TABLE, FIELD>(mdb, sub_info);
    }
    template<typename TABLE>
    void init_table_rebuilder(uint32_t msgid, mm_front_api_mdb *mdb, front_api::mm_front_api::subscribe_info *sub_info =nullptr) {
        XLOG(XLOG_DEBUG, "INIT_TABLE_REBUILDER\n");
        mdb->register_table<TABLE>();
        rebuilders_[msgid] = new mm_front_api_table_rebuilder<TABLE>(mdb, sub_info);
    }
    void init_rsp_func(uint32_t msgid, const rsp_callback_type &rsp_func) {
        rsp_callback_map_.insert(std::make_pair(msgid, rsp_func));
    }

private:
    void on_connected(const cffex::net_addr &ep);
    void on_disconnected(const cffex::net_addr &ep);
    void on_data(const cffex::biz_decoder *d);
    void on_rsp(const cffex::system::error_code &ec,
                uint32_t                         request_id,
                const cffex::biz_decoder        *d,
                bool                             is_last,
                void                            *context);

private:
    using rsp_callback_map_type  = std::map<uint32_t, rsp_callback_type>;
    using rebuilder_map_type     = std::map<uint32_t, base_rebuilder *>;

    cffex::biz_client       *client_{nullptr};
    cffex::io_service_thread thread_;
    net_callback_type       *connected_cb_{nullptr};
    net_callback_type       *disconnected_cb_{nullptr};
    error_callback_type     *error_cb_{nullptr};
    rsp_callback_map_type    rsp_callback_map_;
    rebuilder_map_type       rebuilders_;
};

}  // namespace mm
}  // namespace cffex

#endif