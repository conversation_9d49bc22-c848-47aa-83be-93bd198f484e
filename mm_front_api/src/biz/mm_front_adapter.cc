/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: jinfy
 * Date: 2023-09-12
 */

#include "mm_front_adapter.h"

#include <cffex/log/xlog.h>
#include <cffex/pattern/function.h>

#include <cstdint>
#include <utility>

#include "mm_common.h"
#include "mm_msgid.h"

using namespace cffex::mm;

mm_front_adapter::mm_front_adapter() {}
mm_front_adapter::~mm_front_adapter() {
    // todo delete cb
    for (rebuilder_map_type::iterator itor = rebuilders_.begin(); itor != rebuilders_.end(); ++itor) {
        delete itor->second;
    }
    rebuilders_.clear();
}
void mm_front_adapter::init(const char *pub_addr, const char *req_addr) {
    client_ = new cffex::biz_client(&thread_, 0);
    client_->init_topic(MM_TOPIC_PY_STRATEGY_SERVICE, pub_addr);
    client_->set_subscribe_callback(MM_TOPIC_PY_STRATEGY_SERVICE,
                                    cffex::bind(&mm_front_adapter::on_data, this, cffex::_1),
                                    cffex::biz_client::BIZ_SUB_FROM_BEGIN);
    client_->set_connected_callback(MM_TOPIC_PY_STRATEGY_SERVICE,
                                    cffex::bind(&mm_front_adapter::on_connected, this, cffex::_1));
    client_->set_closed_callback(MM_TOPIC_PY_STRATEGY_SERVICE,
                                 cffex::bind(&mm_front_adapter::on_disconnected, this, cffex::_1));

    client_->init_topic(MM_TOPIC_FRONT_SERVICE, req_addr);
    client_->set_connected_callback(MM_TOPIC_FRONT_SERVICE,
                                    cffex::bind(&mm_front_adapter::on_connected, this, cffex::_1));
    client_->set_closed_callback(MM_TOPIC_FRONT_SERVICE,
                                 cffex::bind(&mm_front_adapter::on_disconnected, this, cffex::_1));
    client_->set_response_callback(MM_TOPIC_FRONT_SERVICE,
                                   cffex::bind(&mm_front_adapter::on_rsp,
                                               this,
                                               cffex::_1,
                                               cffex::_2,
                                               cffex::_3,
                                               cffex::_4,
                                               cffex::_5));

    GXLOG(XLOG_INFO,
          "mm_front_adapter::%s, pub_addr:[%s], req_addr[%s]\n",
          __FUNCTION__,
          pub_addr,
          req_addr);
}
void mm_front_adapter::start() {
    thread_.start();
}
void mm_front_adapter::stop() {
    thread_.stop();
}
void mm_front_adapter::join() {
    thread_.join();
}
cffex::io_service_thread *mm_front_adapter::get_service_thread() {
    return &thread_;
}
void mm_front_adapter::set_connected_callback(const net_callback_type &cb) {
    if (connected_cb_) {
        delete connected_cb_;
        connected_cb_ = nullptr;
    }
    connected_cb_ = new net_callback_type(cb);
}
void mm_front_adapter::set_disconnected_callback(const net_callback_type &cb) {
    if (disconnected_cb_) {
        delete disconnected_cb_;
        disconnected_cb_ = nullptr;
    }
    disconnected_cb_ = new net_callback_type(cb);
}
void mm_front_adapter::set_error_callback(const error_callback_type &cb) {
    if (error_cb_) {
        delete error_cb_;
        error_cb_ = nullptr;
    }
    error_cb_ = new error_callback_type(cb);
}
void mm_front_adapter::insert_rsp_callback(uint32_t msgid, const rsp_callback_type &cb) {
    rsp_callback_map_.insert(std::make_pair(msgid, cb));
}

int mm_front_adapter::send_req(cffex::biz::biz_encoder &en, uint32_t request_id) {
    GXLOG(XLOG_DEBUG, "mm_front_adapter::%s, request_id[%d]\n", __FUNCTION__, request_id);
    return client_->request(MM_TOPIC_FRONT_SERVICE, request_id, en);
}

void mm_front_adapter::on_connected(const cffex::net_addr &ep) {
    GXLOG(XLOG_INFO, "mm_front_adapter::%s, [%s:%d]\n", __FUNCTION__, ep.ip(), ep.port());
    if (connected_cb_) {
        (*connected_cb_)(ep.ip());
    }
}
void mm_front_adapter::on_disconnected(const cffex::net_addr &ep) {
    GXLOG(XLOG_INFO, "mm_front_adapter::%s, [%s:%d]\n", __FUNCTION__, ep.ip(), ep.port());
    if (disconnected_cb_) {
        (*disconnected_cb_)(ep.ip());
    }
}
void mm_front_adapter::on_data(const cffex::biz_decoder *d) {
    GXLOG(XLOG_INFO, "mm_front_adapter::%s, msgid[0X%04X-%s]\n", __FUNCTION__, d->get_msgid(), get_str_msg_id(d->get_msgid()));
    rebuilder_map_type::iterator itor = rebuilders_.find(d->get_msgid());
    if (itor == rebuilders_.end()) {
        GXLOG(XLOG_WARNING, "mm_front_adapter::%s, no rebuilder found for msgid[0X%04X]\n", __FUNCTION__, d->get_msgid());
        return;
    }
    GXLOG(XLOG_INFO, "mm_front_adapter::%s, calling rebuilder for msgid[0X%04X]\n", __FUNCTION__, d->get_msgid());
    itor->second->on_rebuild(d);
}
void mm_front_adapter::on_rsp(const cffex::system::error_code &ec,
                              uint32_t                         request_id,
                              const cffex::biz_decoder        *d,
                              bool                             is_last,
                              void                            *context) {
    GXLOG(XLOG_DEBUG,
          "mm_front_adapter::%s, requst_id[%d], ec[%d:%s], [%s]\n",
          __FUNCTION__,
          request_id,
          ec,
          cffex::system::error::get_error_msg(ec),
          d == nullptr ? "" : d->to_string().c_str());
    if (ec != 0 || d == nullptr) {
        (*error_cb_)(ec, cffex::system::error::get_error_msg(ec), request_id);
        return;
    }

    rsp_info_field f;
    if (rsp_callback_map_.find(d->get_msgid()) == rsp_callback_map_.end()) {  // common rsp
        if (0 != d->get_field(f)) {
            f.error_id  = d->get_code();
            f.error_msg = cffex::system::error::get_error_msg(d->get_code());
        }
        (*error_cb_)(f.error_id.get_value(), f.error_msg.get_value(), request_id);
        return;
    }

    if (0 == d->get_field(f)) {
        (*error_cb_)(f.error_id.get_value(), f.error_msg.get_value(), request_id);
        return;
    }

    rsp_callback_map_[d->get_msgid()](d, request_id, is_last);
}
