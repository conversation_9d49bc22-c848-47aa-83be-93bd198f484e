/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: pengyc
 * Date: 2025-05-16
 */

#ifndef CFFEX_MM_FRONT_API_REBUILDER_H
#define CFFEX_MM_FRONT_API_REBUILDER_H

#include "mm_rebuilder.h"
#include "mm_front_api_mdb.h"
#include "mm_front_api.h"
#include <type_traits>

namespace cffex {
namespace mm {

// 检查字段类型是否包含instrument_id成员的模板
template<typename T, typename = void>
struct has_instrument_id : std::false_type {};

template<typename T>
struct has_instrument_id<T, std::void_t<decltype(std::declval<T>().instrument_id)>> : std::true_type {};

template <typename TABLE, typename FIELD>
class mm_front_api_custom_rebuilder : public base_rebuilder
{
public:
    mm_front_api_custom_rebuilder(mm_front_api_mdb *mdb, front_api::mm_front_api::subscribe_info *sub_info = nullptr)
        : mdb_(mdb), subscribe_info_(sub_info) { }
    virtual ~mm_front_api_custom_rebuilder() { }

    virtual void on_rebuild(const biz_decoder *d)
    {
        XLOG(XLOG_DEBUG, "mm_front_api_custom_rebuilder::enter this new rebuidler\n");
        FIELD f;
        for (cffex::biz::biz_decoder::const_iterator itr = d->begin(); itr != d->end(); ++itr) {
            if ( 0 != d->get_field(f, itr)) {
                XLOG(XLOG_WARNING, "mm_front_api_rebuilder::%s, get_field[%s] failed\n", __FUNCTION__, TABLE::field_type::NAME());
                continue;
            }

            // 检查是否需要进行instrument过滤
            if constexpr (has_instrument_id<FIELD>::value) {
                if (subscribe_info_ != nullptr) {
                    const char* instrument_id = f.instrument_id.get_value();
                    if (!subscribe_info_->is_instrument_subscribed(instrument_id)) {
                        XLOG(XLOG_TRACE, "mm_front_api_custom_rebuilder::%s, instrument [%s] not subscribed, filtered\n",
                              __FUNCTION__, instrument_id);
                        continue;  // 过滤掉未订阅的合约
                    }
                }
            }

            if constexpr(std::is_same<FIELD, security_instrument_field>::value){
                XLOG(XLOG_DEBUG, "mm_front_api_rebuilder::%s,\n");
                mdb_->add_or_update(f, mdb_->get_table<security_instrument_table>());
            }
            if constexpr(std::is_same<FIELD, future_instrument_field>::value){
                XLOG(XLOG_DEBUG, "mm_front_api_rebuilder::%s,\n");
                mdb_->add_or_update(f, mdb_->get_table<future_instrument_table>());
            }
            if constexpr(std::is_same<FIELD, option_instrument_field>::value){
                XLOG(XLOG_DEBUG, "mm_front_api_rebuilder::%s,\n");
                mdb_->add_or_update(f, mdb_->get_table<option_instrument_table>());
            }
            mdb_->add_or_update(f, mdb_->get_table<TABLE>());
        }
    }

private:
    mm_front_api_mdb                 *mdb_;
    front_api::mm_front_api::subscribe_info *subscribe_info_;
};

template <typename TABLE>
class mm_front_api_table_rebuilder : public base_rebuilder
{
public:
    mm_front_api_table_rebuilder(mm_front_api_mdb *mdb, front_api::mm_front_api::subscribe_info *sub_info = nullptr)
        : mdb_(mdb), subscribe_info_(sub_info) { }
    virtual ~mm_front_api_table_rebuilder() { }

    virtual void on_rebuild(const biz_decoder *d)
    {
        XLOG(XLOG_DEBUG, "mm_front_api_table_rebuilder::enter this new rebuidler\n");
        typename TABLE::field_type f;
        for (cffex::biz::biz_decoder::const_iterator itr = d->begin(); itr != d->end(); ++itr) {
            if ( 0 != d->get_field(f, itr)) {
                XLOG(XLOG_WARNING, "mm_front_api_rebuilder::%s, get field[%s] failed\n", __FUNCTION__, TABLE::field_type::NAME());
                continue;
            }

            // 检查是否需要进行instrument过滤
            if constexpr (has_instrument_id<typename TABLE::field_type>::value) {
                if (subscribe_info_ != nullptr) {
                    const char* instrument_id = f.instrument_id.get_value();
                    if (!subscribe_info_->is_instrument_subscribed(instrument_id)) {
                        XLOG(XLOG_TRACE, "mm_front_api_table_rebuilder::%s, instrument [%s] not subscribed, filtered\n",
                              __FUNCTION__, instrument_id);
                        continue;  // 过滤掉未订阅的合约
                    }
                }
            }

            mdb_->add_or_update(f, mdb_->get_table<TABLE>());
        }
    }

private:
    mm_front_api_mdb                 *mdb_;
    front_api::mm_front_api::subscribe_info *subscribe_info_;
};

}
}

#endif