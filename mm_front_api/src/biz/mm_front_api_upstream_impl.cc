/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: jinfy
 * Date: 2023-09-12
 */

#include <cffex/log/xlog.h>

#include <cstdio>

#include "mm_field.h"
#include "mm_front_api_entity_impl.h"
#include "mm_front_api_impl.h"
#include "mm_front_api_msg_impl.h"
#include "mm_front_api_puber.h"
#include "mm_front_api_version.h"
#include "mm_msgid.h"
#include "mm_type.h"
#include "mm_id_manager.h"

using namespace cffex::mm;

front_api::mm_front_api::subscribe_info *
front_api::mm_front_api::subscribe_info::create_subscribe_info() {
    return new subscribe_info_impl();
}

void subscribe_info_impl::append(const char *table, int interval) {
    sub_info_map_[std::string(table)] = sub_detail{table, interval};
}

bool subscribe_info_impl::first(char table[64], int &interval) {
    sub_info_map_itor_ = sub_info_map_.begin();
    return next(table, interval);
}

bool subscribe_info_impl::next(char table[64], int &interval) {
    if (sub_info_map_itor_ == sub_info_map_.end()) {
        return false;
    }
    sub_detail *d = &(sub_info_map_itor_->second);
    int table_len = strlen(d->name.c_str());
    if (table_len >= 64) {
        GXLOG(XLOG_WARNING, "subscribe_info_impl::%s, contains long table name [%s]\n", __FUNCTION__, d->name.c_str());
        return true;
    }
    memcpy(table, d->name.c_str(), table_len);
    table[table_len] = 0;
    interval = d->interval;
    sub_info_map_itor_++;
    return true;
}

// 合约订阅相关方法实现
void subscribe_info_impl::append_instrument(const char *instrument_id) {
    subscribe_instruments_.insert(std::string(instrument_id));
}

bool subscribe_info_impl::first_instrument(char instrument_id[64]) {
    subscribe_instruments_itor_ = subscribe_instruments_.begin();
    return next_instrument(instrument_id);
}

bool subscribe_info_impl::next_instrument(char instrument_id[64]) {
    if (subscribe_instruments_itor_ == subscribe_instruments_.end()) {
        return false;
    }
    const std::string& id = *subscribe_instruments_itor_;
    int id_len = id.length();
    if (id_len >= 64) {
        GXLOG(XLOG_WARNING, "subscribe_info_impl::%s, contains long instrument id [%s]\n", __FUNCTION__, id.c_str());
        return true;
    }
    memcpy(instrument_id, id.c_str(), id_len);
    instrument_id[id_len] = 0;
    subscribe_instruments_itor_++;
    return true;
}

bool subscribe_info_impl::is_instrument_subscribed(const char *instrument_id) {
    // 如果没有设置subscribe_instruments，则不过滤（返回true）
    if (subscribe_instruments_.empty()) {
        return true;
    }
    // 检查合约是否在订阅列表中
    return subscribe_instruments_.find(std::string(instrument_id)) != subscribe_instruments_.end();
}

void subscribe_info_impl::set_enable_instrument_filter(bool enable) {
    enable_instrument_filter_ = enable;
}

bool subscribe_info_impl::is_instrument_filter_enabled() {
    return enable_instrument_filter_;
}

front_api::mm_front_api *front_api::mm_front_api::create_mm_front_api(unsigned short node_id,
                                                                      const char    *log_level,
                                                                      const char    *log_dir) {
    return new mm_front_api_impl(node_id, log_level, log_dir);
}

mm_front_api_impl::mm_front_api_impl(unsigned short node_id, const char *log_level, const char *log_dir) :
    node_id_(node_id), limiter_(adapter_.get_service_thread()) {
    if (log_dir != nullptr && strlen(log_dir) != 0) {
        std::string log_file{log_dir};
        SET_XLOG_FILE(log_file.c_str());
        std::string level{log_level};
        if (level == "trace") {
            SET_XLOG_LEVEL(XLOG_TRACE);
        } else if (level == "debug") {
            SET_XLOG_LEVEL(XLOG_DEBUG);
        } else if (level == "info") {
            SET_XLOG_LEVEL(XLOG_INFO);
        } else if (level == "warning") {
            SET_XLOG_LEVEL(XLOG_WARNING);
        } else if (level == "error") {
            SET_XLOG_LEVEL(XLOG_ERROR);
        } else if (level == "fatal") {
            SET_XLOG_LEVEL(XLOG_FATAL);
        } else if (level == "all") {
            SET_XLOG_LEVEL(XLOG_ALL);
        } else {
            SET_XLOG_LEVEL(XLOG_ALL);
        }
        GXLOG(XLOG_INFO, "mm_front_api_impl::%s, version: [%s] log_level[%s] log_dir[%s]\n", 
              __FUNCTION__, 
              MM_FRONT_API_VERSION,
              level.c_str(),
              log_file.c_str());
    }
    do_init();
}
mm_front_api_impl::~mm_front_api_impl() {}

void mm_front_api_impl::release() {
    adapter_.stop();
}

void mm_front_api_impl::init(const char *pub_addr, const char *req_addr, subscribe_info *sub_info) {
    adapter_.init(pub_addr, req_addr);

    adapter_.set_connected_callback(cffex::bind(&mm_front_api_impl::on_connected, this, cffex::_1));
    adapter_.set_disconnected_callback(cffex::bind(&mm_front_api_impl::on_disconnected, this, cffex::_1));
    adapter_.set_error_callback(
        cffex::bind(&mm_front_api_impl::on_rsp_info, this, cffex::_1, cffex::_2, cffex::_3));

    if (sub_info == nullptr) { return; }

    // 存储subscribe_info以供消息过滤使用
    subscribe_info_ = sub_info;

    // 处理表订阅
    char table_name[128] = { 0 };
    int  table_interval = 0;
    for (bool ret = sub_info->first(table_name, table_interval); ret; ret = sub_info->next(table_name, table_interval)) {
        GXLOG(XLOG_INFO, "mm_front_api_impl::%s, sub table [%s] interval[%d]\n", __FUNCTION__, table_name, table_interval);
        const char *suffix = table_interval > 0 ? "_snap" : "_table";
        strcat(table_name, suffix);
        i_mm_front_api_puber *publisher = puber_funcs_[table_name]();
        if (table_interval > 0) {
            limiter_.register_task(publisher, table_interval);
        }
        memset(table_name, 0, sizeof(table_name));
    }

    // 处理合约订阅
    char instrument_id[64] = { 0 };
    for (bool ret = sub_info->first_instrument(instrument_id); ret; ret = sub_info->next_instrument(instrument_id)) {
        GXLOG(XLOG_INFO, "mm_front_api_impl::%s, sub instrument [%s]\n", __FUNCTION__, instrument_id);
        memset(instrument_id, 0, sizeof(instrument_id));
    }
}

void mm_front_api_impl::start() {
    limiter_.start();
    adapter_.start();
}

int mm_front_api_impl::join() {
    adapter_.join();
    return 0;
}

void mm_front_api_impl::register_spi(front_api::mm_front_spi *spi) {
    spi_ = spi;
}
