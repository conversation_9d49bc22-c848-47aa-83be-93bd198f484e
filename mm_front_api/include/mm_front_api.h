/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 * 
 * Author:  Febao
 * Date:    2025-07-28 10:49:05
 * Version: 0.0.7
 */

#ifndef CFFEX_MM_FRONT_API_H
#define CFFEX_MM_FRONT_API_H


#include "mm_front_api_entity.h"
#include "mm_front_spi.h"

namespace cffex {
namespace mm {
namespace front_api {

class mm_front_api_entity_creator;

class mm_front_api {
public:
    class subscribe_info {
    public:
        static subscribe_info *create_subscribe_info();
        virtual void append(const char *table, int interval = 0) = 0;
        virtual bool first(char table[64], int &interval) = 0;
        virtual bool next(char table[64], int &interval) = 0;

        // 合约订阅相关方法
        virtual void append_instrument(const char *instrument_id) = 0;
        virtual bool first_instrument(char instrument_id[64]) = 0;
        virtual bool next_instrument(char instrument_id[64]) = 0;
        virtual bool is_instrument_subscribed(const char *instrument_id) = 0;

        // 合约过滤开关
        virtual void set_enable_instrument_filter(bool enable) = 0;
        virtual bool is_instrument_filter_enabled() = 0;

    protected:
        ~subscribe_info() {}
    };

public:
    static mm_front_api *create_mm_front_api(unsigned short node_id = 1, const char* log_level="all", const char *log_dir="./");

    virtual void release() = 0;
    virtual void init(const char *pub_addr, const char *req_addr, subscribe_info *sub_info) = 0;
    virtual void start() = 0;
    virtual int  join() = 0;
    virtual void register_spi(mm_front_spi *spi) = 0;

    virtual mm_front_api_entity_creator *get_entity_creator() = 0;

    virtual int  insert_order(order_entity *order, int request_id = 0) = 0;
    virtual int  cancel_order(order_entity *order, int request_id = 0) = 0;
    virtual int  modify_instrument_param_value(instrument_param_value_entity *instrument_param_value, int request_id = 0) = 0;
    virtual int  modify_custom_param_value(custom_param_value_entity *custom_param_value, int request_id = 0) = 0;

protected:
    ~mm_front_api() {}
};

}
}
}

#endif
