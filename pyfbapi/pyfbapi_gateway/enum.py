'''
CFFEX Confidential.

@Copyright 2018 CFFEX.  All rights reserved.

The source code for this program is not published or otherwise
divested of its trade secrets, irrespective of what has been
deposited with the China Copyright Office.

Author:  Febao
Date:    2025-07-28 10:49:05
Version: 0.0.7
'''
from enum import Enum

class IntStatusEnum(Enum):
    '''None'''
    INACTIVE = '0'    #
    ACTIVE = '1'    #
    STATUS_ALL = '2'    #

class ExchangeIdEnum(Enum):
    '''None'''
    EXCHANGE_CFFEX = '0'    #中金所
    EXCHANGE_SHFE = '1'    #上期所
    EXCHANGE_DCE = '2'    #大商所
    EXCHANGE_ZCE = '3'    #郑商所
    EXCHANGE_SSE = '4'    #上交所
    EXCHANGE_SZSE = '5'    #深交所
    EXCHANGE_INE = '6'    #能源交易中心
    EXCHANGE_GFEX = '7'    #广期所
    EXCHANGE_BSE = '8'    #北交所
    EXCHANGE_UNKNOWN = 'n'    #
    EXCHANGE_ALL = 'z'    #

class OptionTypeEnum(Enum):
    '''None'''
    OPTION_CALL = 'c'    #
    OPTION_PUT = 'p'    #

class InstrumentTypeEnum(Enum):
    '''None'''
    INSTRUMENT_FUTURE = '0'    #
    INSTRUMENT_OPTION = '1'    #
    INSTRUMENT_SECURITY = '2'    #
    INSTRUMENT_ARBITRAGE = '3'    #
    INSTRUMENT_TYPE_ALL = '9'    #

class UnitCalEnum(Enum):
    '''None'''
    UNIT_CAL_ABSOLUTE = '0'    #
    UNIT_CAL_PERCENT = '1'    #
    UNIT_CAL_TICK = '2'    #
    UNIT_METHOD_ALL = '3'    #

class QuoteSourceEnum(Enum):
    '''None'''
    QUOTE_SOURCE_MANUAL_QUOTE = '1'    #
    QUOTE_SOURCE_STRATEGY_QUOTE = '2'    #
    QUOTE_SOURCE_OUTSIDE_QUOTE = '3'    #
    QUOTE_SOURCE_ALL = 'A'    #

class OrderSourceEnum(Enum):
    '''None'''
    ORDER_SOURCE_BATCH_ORDER = '0'    #
    ORDER_SOURCE_MANUAL_ORDER = '1'    #
    ORDER_SOURCE_MANUAL_QUOTE_ORDER = '2'    #
    ORDER_SOURCE_STRATEGY_ORDER = '3'    #
    ORDER_SOURCE_STRATEGY_QUOTE_ORDER = '4'    #
    ORDER_SOURCE_OUTSIDE_ORDER = '5'    #
    ORDER_SOURCE_OUTSIDE_QUOTE_ORDER = '6'    #
    ORDER_SOURCE_MANUAL_CREATE_REDEMPT_ORDER = '7'    #
    ORDER_SOURCE_STRATEGY_CREATE_REDEMPT_ORDER = '8'    #
    ORDER_SOURCE_OUTSIDE_CREATE_REDEMPT_ORDER = '9'    #
    ORDER_SOURCE_FUND_ARBITRAGE_ORDER = 'A'    #基金套利
    ORDER_SOURCE_MANUAL_BASKET_ORDER = 'B'    #篮子买卖
    ORDER_SOURCE_INSTANT_CREATE_REDEMPT_ORDER = 'C'    #一键申赎
    ORDER_SOURCE_MANUAL_CANCEL_PADDING_ORDER = 'D'    #手动撤补
    ORDER_SOURCE_MANUAL_PADDING_ORDER = 'E'    #手动补单
    ORDER_SOURCE_ALL = 'N'    #

class OffsetFlagEnum(Enum):
    '''None'''
    OFFSET_FLAG_OPEN = '1'    #
    OFFSET_FLAG_CLOSE = '2'    #
    OFFSET_FLAG_CLOSE_TODAY = '3'    #
    OFFSET_FLAG_CLOSE_YESTERDAY = '4'    #
    OFFSET_FLAG_AUTO = '5'    #

class HedgeFlagEnum(Enum):
    '''None'''
    HEDGE_FLAG_SPECULATION = '1'    #投机
    HEDGE_FLAG_ARBITRAGE = '2'    #套利
    HEDGE_FLAG_HEDGE = '3'    #套保
    HEDGE_FLAG_MARKET_MAKER = '4'    #做市商

class QuoteStatusEnum(Enum):
    '''None'''
    QUOTE_STATUS_NONE = '0'    #未知状态
    QUOTE_STATUS_WAITING = '1'    #已报单，待确认
    QUOTE_STATUS_IN_BOOK = '2'    #交易所确认
    QUOTE_STATUS_PART_CANCEL = '3'    #部分订单取消
    QUOTE_STATUS_ALL_CANCEL = '4'    #全部订单取消
    QUOTE_STATUS_ERROR = '5'    #订单错误
    QUOTE_STATUS_PART_TRADED = '6'    #部分成交
    QUOTE_STATUS_ALL_TRADED = '7'    #全部成交
    QUOTE_STATUS_TIMEOUT = '8'    #超时状态

class OrderStatusEnum(Enum):
    '''None'''
    ORDER_STATUS_NONE = '0'    #未知状态
    ORDER_STATUS_WAITING = '1'    #已报单，待确认
    ORDER_STATUS_IN_BOOK = '2'    #交易所确认
    ORDER_STATUS_CANCEL = '3'    #订单取消
    ORDER_STATUS_ERROR = '4'    #订单错误
    ORDER_STATUS_PART_TRADED = '5'    #部分成交
    ORDER_STATUS_ALL_TRADED = '6'    #全部成交
    ORDER_STATUS_TIMEOUT = '7'    #超时状态

class DirectionEnum(Enum):
    '''None'''
    DIRECTION_BUY = '0'    #买
    DIRECTION_SELL = '1'    #卖

class PriceCategoryEnum(Enum):
    '''None'''
    PRICE_CATEGORY_ANY = '1'    #任意价
    PRICE_CATEGORY_LIMIT = '2'    #限价
    PRICE_CATEGORY_BEST = '3'    #对手方最优价
    PRICE_CATEGORY_FIVE_LEVEL = '4'    #对手方最优五档
    PRICE_CATEGORY_ARBITRAGE = '5'    #套利
    PRICE_CATEGORY_SWAP = '6'    #互换
    PRICE_CATEGORY_BOTH = '7'    #报价衍生
    PRICE_CATEGORY_OTHER = '8'    #其他
    PRICE_CATEGORY_OWN_BEST = '9'    #本方最优价

class TimeConditionEnum(Enum):
    '''None'''
    TIME_CONDITION_IOC = '1'    #
    TIME_CONDITION_GFD = '2'    #

class VolumeConditionEnum(Enum):
    '''None'''
    VOLUME_ANY = '1'    #任意数量
    VOLUME_COMPLETE = '2'    #全部数量

class HedgePriceEnum(Enum):
    '''None'''
    BEST_PRICE = '1'    #
    OPPONENT_PRICE = '2'    #
    LAST_PRICE = '3'    #
    LIMIT_PRICE = '4'    #

class RoudingModeEnum(Enum):
    '''None'''
    ROUNDING_MODEL_CEIL = '1'    #舍入， 向上取整
    ROUNDING_MODEL_FLOOR = '2'    #舍出， 向下取整
    ROUNDING_MODEL_ROUND = '3'    #四舍五入

class SpreadTemplateTypeEnum(Enum):
    '''None'''
    QUOTE_SPREAD_TEMPLATE = '1'    #报价价差模板
    INQUIRY_SPREAD_TEMPLATE = '2'    #回应询价价差模板

class SpreadTemplateAlgorithmEnum(Enum):
    '''None'''
    TEMPLATE_ALGO_CONSTANT = '1'    #常量价差模板
    TEMPLATE_ALGO_MAX = '2'    #最大值价差模板

class UserTypeEnum(Enum):
    '''None'''
    ADMIN_TYPE = '1'    #管理员类型
    TRADER_TYPE = '2'    #交易员类型
    RISK_TYPE = '3'    #风控员类型
    QUERIER_TYPE = '4'    #查询员类型
    ACCOUNT_MANAGER_TYPE = '5'    #账户管理员类型
    OPERATOR_MAINTAINER_TYPE = '6'    #运维员类型

class UserSessionStatusEnum(Enum):
    '''None'''
    LOGIN_STATUS = '1'    #已登录
    UNLOGIN_STATUS = '2'    #未登录
    MULTI_LOGIN_STATUS = '3'    #重复登录 当前会话失效
    PASS_OVERDUE_STATUS = '4'    #密码重置 当前会话失效
    TIMEOUT_STATUS = '5'    #心跳超时 当前会话失效
    INACTIVE_ACCOUNT_STATUS = '6'    #帐号未激活 当前会话失效
    ACCOUNT_ROLE_CHANGED = '7'    #帐号权限变更 当前会话失效

class LoginFlagEnum(Enum):
    '''None'''
    NORMAL_LOGIN = '0'    #正常登录
    FORCE_LOGIN = '1'    #强制登录

class StrategyStateEnum(Enum):
    '''None'''
    STRATEGY_INIT_STAT = '1'    #初始状态
    STRATEGY_RUNNING_STAT = '2'    #运行中
    STRATEGY_PAUSE_STAT = '3'    #已暂停
    STRATEGY_DELETE_STAT = '4'    #已删除
    STRATEGY_TIMEOUT_STAT = '5'    #已失效

class StrategyLogLevelEnum(Enum):
    '''None'''
    STRATEGY_DEBUG = '0'    #调试日志
    STRATEGY_INFO = '1'    #正常日志
    STRATEGY_WARNING = '2'    #警告日志
    STRATEGY_ERROR = '3'    #错误日志

class InstrumentTradingStatusEnum(Enum):
    '''None'''
    UNKNOWN = '0'    #未知状态
    BEFORE_TRADING = '1'    #开盘前
    NOTRADING = '2'    #非交易
    CONTINOUS = '3'    #连续交易
    AUCTION_ORDERING = '4'    #集合竞价报单
    AUCTION_MATCH = '5'    #集合竞价撮合
    CLOSED = '6'    #收盘
    SUSPENDED = '7'    #停牌
    CIRCUIT_BREAKER = '8'    #熔断
    VOLATILITY_DISRUPTION = '9'    #波动性中断
    INQUIRY = 'A'    #询价中
    CLOSED_AUCTION_ORDERING = 'B'    #收盘集合竞价
    AFTER_TRADING = 'C'    #盘后交易

class PortfolioRightEnum(Enum):
    '''None'''
    READ_ONLY = '0'    #只读
    TRADABLE = '1'    #可交易

class IsTradingDayEnum(Enum):
    '''None'''
    NON_TRADING_DAY = '0'    #非交易日
    TRADING_DAY = '1'    #交易日

class PositionTypeEnum(Enum):
    '''None'''
    LONG_POSITION = '0'    #多头持仓
    SHORT_POSITION = '1'    #空头持仓

class RiskCategoryEnum(Enum):
    '''None'''
    RISK_POSITION_LIMIT = '0'    #持仓限额
    RISK_MAX_ORDER_LIMIT = '1'    #最大报单限额
    RISK_FLOW_CONTROL = '2'    #流控
    RISK_SELF_TRADE_INSIDE = '3'    #系统内防对敲
    RISK_SELF_TRADE_OUTSIDE = '4'    #系统间防对敲
    RISK_TRADING_RIGHT = '5'    #交易权限
    RISK_GREEKS = '6'    #希腊字母
    RISK_DEVIATION = '7'    #价格偏离度
    RISK_PRICE_LIMIT = '8'    #涨跌停限制
    RISK_MAX_CANCEL_ORDER_LIMIT = '9'    #最大撤单限额
    RISK_FUND_POSITION_COST_LIMIT = 'A'    #基金持仓成本限额
    RISK_FUND_POSITION_CONTENTRATION_RATIO_LIMIT = 'B'    #持仓集中度限额
    RISK_WEEK_SUM_TRADE_LIMIT = 'C'    #自然周成交限额

class PortfolioTypeEnum(Enum):
    '''None'''
    NO_VIRTUAL = '0'    #非虚拟组合
    VIRTUAL = '1'    #虚拟组合

class VolatilityModelTypeEnum(Enum):
    '''None'''
    WING_STATIC_MODEL = '1'    #wing静态模型
    CUBIC_SPLINE_STATIC_MODEL = '2'    #cubic模型
    WING_DYNAMIC_MODEL = '3'    #wing动态模型
    CUBIC_SPLINE_ROLLING_MODEL = '4'    #cubic rolling模型

class VolatilitySourceTypeEnum(Enum):
    '''None'''
    PREDICT_VOLATILITY = '0'    #预测波动率
    FIX_VOLATILITY = '1'    #固定波动率

class RateSourceTypeEnum(Enum):
    '''None'''
    PREDICT_RATE = '0'    #利率曲线
    FIX_RATE = '1'    #固定利率

class CloseTypeEnum(Enum):
    '''None'''
    PRFER_CLOSE_TODAY = '1'    #优先平今
    PRFER_CLOSE_YESTODAY = '2'    #优先平昨

class OutsideSelfTradeTypeEnum(Enum):
    '''None'''
    OUTSIDE_SELF_TRADE_NO_CHECK = '0'    #不检查
    OUTSIDE_SELF_TRADE_CHECK_WAITING = '1'    #待检查
    OUTSIDE_SELF_TRADE_CHECK_PASS = '2'    #检查通过
    OUTSIDE_SELF_TRADE_CHECK_FAILED = '3'    #检查不通过

class TradeSourceEnum(Enum):
    '''None'''
    TRADE_SOURCE_INIT = '1'    #初始化
    TRADE_SOURCE_MANUAL = '2'    #手动单
    TRADE_SOURCE_STRATEGY = '3'    #策略单
    TRADE_SOURCE_OUTSIDE = '4'    #外部流水
    TRADE_SOURCE_OUTSIDE_COMB = '5'    #外部流水组合
    TRADE_SOURCE_BOOK = '6'    #簿记
    TRADE_SOURCE_CREATION_REDEMPTION = '7'    #簿记申赎
    TRADE_SOURCE_OUTSIDE_CREATION_REDEMPTION = '8'    #外部流水申赎
    TRADE_SOURCE_SETTLEMENT = 'A'    #结算成交
    TRADE_SOURCE_MARGIN_TRADING = 'B'    #簿记融券可卖
    TRADE_SOURCE_INIT_CREATION_REDEMPTION = 'C'    #初始化申赎单
    TRADE_SOURCE_MANUAL_CREATION_REDEMPTION = 'D'    #手动申赎单
    TRADE_SOURCE_STRATEGY_CREATION_REDEMPTION = 'E'    #策略申赎单
    TRADE_SOURCE_BOOK_TDPOSITION = 'F'    #簿记今持仓
    TRADE_SOURCE_BOOK_CRPOSITION = 'G'    #簿记申赎持仓

class InquiryQuoteStatusEnum(Enum):
    '''None'''
    INQUIRY_QUOTE_WAITING = '0'    #等待回应
    INQUIRY_QUOTE_FINISH = '1'    #已回应
    INQUIRY_QUOTE_TIMEOUT = '2'    #回应超时

class ValidQuoteEnum(Enum):
    '''None'''
    INVALID_QUOTE = '0'    #无效报价
    VALID_QUOTE = '1'    #有效报价

class CombActionEnum(Enum):
    '''None'''
    COMB_COMBINE = '0'    #组合申报
    COMB_SPLIT = '1'    #组合拆分

class CombStrategyEnum(Enum):
    '''None'''
    COMB_STRATEGY_CNSJC = '0'    #认购牛市价差
    COMB_STRATEGY_CXSJC = '1'    #认购熊市价差
    COMB_STRATEGY_PNSJC = '2'    #认沽牛市价差
    COMB_STRATEGY_PXSJC = '3'    #认沽熊市价差
    COMB_STRATEGY_KS = '4'    #跨式空头
    COMB_STRATEGY_KKS = '5'    #宽跨式空头
    COMB_STRATEGY_SP = '6'    #期货跨期
    COMB_STRATEGY_SPC = '7'    #期货跨品种
    COMB_STRATEGY_DS = '8'    #期货对锁
    COMB_STRATEGY_DSO = '9'    #期权对锁
    COMB_STRATEGY_STD = 'a'    #期权跨式
    COMB_STRATEGY_STG = 'b'    #期权宽跨式
    COMB_STRATEGY_BVS = 'c'    #买入垂直价差
    COMB_STRATEGY_SVS = 'd'    #卖出垂直价差
    COMB_STRATEGY_PRT_BUY = 'e'    #买入期权期货组合
    COMB_STRATEGY_PRT_SELL = 'f'    #卖出期权期货组合
    COMB_STRATEGY_UNKNOWN = 'n'    #未知

class CombSourceEnum(Enum):
    '''None'''
    COMB_SOURCE_INIT = '0'    #
    COMB_SOURCE_MANUAL_COMB = '1'    #
    COMB_SOURCE_STRATEGY_COMB = '2'    #
    COMB_SOURCE_OUTSIDE_COMB = '3'    #
    COMB_SOURCE_ALL = 'A'    #

class CombStatusEnum(Enum):
    '''None'''
    COMB_STATUS_NONE = '0'    #未知状态
    COMB_STATUS_WAITING = '1'    #已申报，待确认
    COMB_STATUS_IN_COMB = '2'    #组合成功
    COMB_STATUS_PART_SPLIT = '3'    #部分拆分
    COMB_STATUS_ALL_SPLIT = '4'    #全部拆分
    COMB_STATUS_ERROR = '5'    #组合错误
    COMB_STATUS_TIMEOUT = '6'    #超时状态

class ExpireDateTypeEnum(Enum):
    '''None'''
    RECENT_MONTH = '1'    #当月合约
    FAR_MONTH = '2'    #远月合约

class LevelEnum(Enum):
    '''None'''
    LEVEL_PORTFOLIO = '0'    #组合
    LEVEL_INSTRUMENT = '1'    #合约
    LEVEL_SERIAL = '2'    #系列
    LEVEL_PRODUCT = '3'    #产品
    LEVEL_EXCHANGE = '4'    #交易所

class ProductTypeEnum(Enum):
    '''None'''
    PRODUCT_FUTURE = '1'    #期货
    PRODUCT_OPTION = '2'    #期权
    PRODUCT_SECURITY = '3'    #现货
    PRODUCT_ARBITRAGE = '4'    #套利组合

class BoolEnum(Enum):
    '''None'''
    MM_FALSE = '0'    #false
    MM_TRUE = '1'    #true

class PortfolioSourceTypeEnum(Enum):
    '''None'''
    PORTFOLIO_TODAY_TRADE = '1'    #今成交默认组合
    PORTFOLIO_YESTERDAY_POSITION = '2'    #昨持仓默认组合

class CounterIdEnum(Enum):
    '''None'''
    COUNTER_FEMAS = '0'    #
    COUNTER_CTP = '1'    #
    COUNTER_XONE = '2'    #
    COUNTER_TAP = '3'    #
    COUNTER_O32 = '4'    #
    COUNTER_FEMASOP = '5'    #
    COUNTER_YD = '6'    #
    COUNTER_APEX = '7'    #
    COUNTER_YLINK = '8'    #
    COUNTER_ROOTNET = '9'    #
    COUNTER_QDP = 'a'    #
    COUNTER_ROHON = 'b'    #
    COUNTER_SHENGLI = 'c'    #
    COUNTER_SIMULATOR = 'd'    #
    COUNTER_ATP = 'e'    #
    COUNTER_EXSECURITY = 'h'    #
    COUNTER_EXFUTURE = 'i'    #
    COUNTER_LDP = 'j'    #
    COUNTER_UNKNOWN = 'u'    #
    COUNTER_ALL = 'z'    #

class ExerciseTypeEnum(Enum):
    '''None'''
    PHYSICAL_DELIVERY = '1'    #实物交割
    CASH_DELIVERY = '2'    #现金交割

class ExerciseStyleEnum(Enum):
    '''None'''
    EXERCISE_STYLE_AMERICAN = '0'    #美式
    EXERCISE_STYLE_EUROPEAN = '1'    #欧式

class CurrentPriceTypeEnum(Enum):
    '''None'''
    CURRENT_PRICE_TYPE_LAST_PRICE = '1'    #最新价
    CURRENT_PRICE_TYPE_MID_PRICE = '2'    #中间价
    CURRENT_PRICE_TYPE_CLOSE_PRICE = '3'    #收盘价
    CURRENT_PRICE_TYPE_UNKNOWN = 'n'    #未配置

class AveragePositionPriceTypeEnum(Enum):
    '''None'''
    AVERAGE_POSITION_PRICE_TYPE_LAST_PRICE = '1'    #最新价
    AVERAGE_POSITION_PRICE_TYPE_PRE_SETTLEMENT_PRICE = '2'    #昨结价
    AVERAGE_POSITION_PRICE_TYPE_CLOSE_PRICE = '3'    #收盘价
    AVERAGE_POSITION_PRICE_TYPE_DERIVED_PRICE = '4'    #理论价
    AVERAGE_POSITION_PRICE_TYPE_UNKNOWN = 'n'    #未配置

class FitContractTypeEnum(Enum):
    '''None'''
    FIT_CONTRACT_TYPE_ALL = '0'    #全部合约
    FIT_CONTRACT_TYPE_CALL = '1'    #看涨合约
    FIT_CONTRACT_TYPE_PUT = '2'    #看跌合约
    FIT_CONTRACT_TYPE_VIRTUAL = '3'    #虚值合约

class FitPriceTypeEnum(Enum):
    '''None'''
    FIT_PRICE_TYPE_LAST = '0'    #最新价
    FIT_PRICE_TYPE_BID = '1'    #买一价
    FIT_PRICE_TYPE_ASK = '2'    #卖一价
    FIT_PRICE_TYPE_MID = '3'    #中间价

class FitRangeTypeEnum(Enum):
    '''None'''
    FIT_RANGE_TYPE_STRIKE = '0'    #执行价范围拟合
    FIT_RANGE_TYPE_DELTA = '1'    #delta范围拟合
    FIT_RANGE_TYPE_ATM = '2'    #百分比范围拟合
    FIT_RANGE_TYPE_LOGMONEYNESS = '3'    #LogMoneyness范围拟合
    FIT_RANGE_TYPE_STRIKE_ABSOLUTE = '4'    #执行价绝对值范围拟合

class AtmForwardTypeEnum(Enum):
    '''None'''
    FIX_ATM_FORWARD = '0'    #固定值
    FOLLOW_ATM_FORWARD = '1'    #跟踪值

class AtmForwardPriceTypeEnum(Enum):
    '''None'''
    ATM_FORWARD_LAST_PRICE = '0'    #最新价
    ATM_FORWARD_BID_PRICE = '1'    #买一价
    ATM_FORWARD_ASK_PRICE = '2'    #卖一价
    ATM_FORWARD_MID_PRICE = '3'    #中间价
    ATM_FORWARD_PRE_SETTLEMENT_PRICE = '4'    #昨结价
    ATM_FORWARD_OPEN_PRICE = '5'    #开盘价
    ATM_FORWARD_BASE_PRICE = '6'    #基准价
    ATM_FORWARD_FORWARD_PRICE = '7'    #远期价
    ATM_FORWARD_THEORETICAL_PRICE = '8'    #理论价
    ATM_FORWARD_UNKNOWN = 'n'    #未知

class GreeksTypeEnum(Enum):
    '''None'''
    GREEKS_UNKNOW = '0'    #未知
    DELTA = '1'    #delta
    GAMMA = '2'    #gamma
    VEGA = '3'    #vega
    CASH_DELTA = '4'    #cash_delta
    CASH_GAMMA = '5'    #cash_gama

class DeviationPriceTypeEnum(Enum):
    '''None'''
    PRICE_UNKNOWN = '0'    #未知
    PRICE_THREO = '1'    #理论价
    PRICE_MID = '2'    #中间价
    PRICE_LAST = '3'    #最新价

class MismatchTypeEnum(Enum):
    '''None'''
    MISMATCH_UNKNOW = '0'    #未知
    MISMATCH_COUNTER = '1'    #柜台
    MISMATCH_FEBAO = '2'    #飞豹

class CheckPhaseTypeEnum(Enum):
    '''None'''
    CHECK_PHASE_UNKNOW = '0'    #未知
    CHECK_PHASE_BEGIN = '1'    #校验开始
    CHECK_PHASE_END = '2'    #校验结束

class VolatilityParamSourceEnum(Enum):
    '''None'''
    VOLATILITY_PARAM_SOURCE_MANUAL = '0'    #手动
    VOLATILITY_PARAM_SOURCE_AUTO = '1'    #自动拟合
    VOLATILITY_PARAM_SOURCE_STRATEGY = '2'    #策略

class SubstituteFlagEnum(Enum):
    '''None'''
    CASH_FORBID = '0'    #禁止现金替代
    CASH_ENABLE = '1'    #可以现金替代
    CASH_FORCE = '2'    #必须现金替代
    CASH_SUPPLY = '3'    #退补现金替代
    CASH_UNKNOWN = 'n'    #未配置

class TradingTypeEnum(Enum):
    '''None'''
    T_0 = '0'    #T+0
    T_1 = '1'    #T+1
    T_UNKNOWN = 'n'    #未配置

class SecurityClassEnum(Enum):
    '''None'''
    SECURITY_FUND = '0'    #基金
    SECURITY_STOCK = '1'    #股票
    SECURITY_BOND = '2'    #债券
    SECURITY_UNKNOWN = 'n'    #未配置

class SecuritySubClassEnum(Enum):
    '''None'''
    FUND_ETF = '0'    #ETF基金
    FUND_RET = '1'    #RET基金
    STOCK_KSH = '2'    #科创板股票
    STOCK_BASE = '3'    #基础层股票
    STOCK_INNOVATION = '4'    #创新层股票
    STOCK_SELECT = '5'    #精选层股票
    STOCK_NORMAL = '6'    #普通股票
    FUND_BOND = '7'    #债券基金
    BOND_NORMAL = '8'    #普通债券
    FUND_GOLD = '9'    #黄金基金
    SECURITY_SUB_UNKNOWN = 'n'    #未配置

class SettlementTypeEnum(Enum):
    '''None'''
    SETTLEMENT_TYPE_EXERCISE = '1'    #行权
    SETTLEMENT_TYPE_ABANDON = '2'    #放弃行权
    SETTLEMENT_TYPE_DELIVERY = '3'    #交割

class MidRiskStatusEnum(Enum):
    '''None'''
    MID_RISK_STATUS_NORMAL = '1'    #正常状态
    MID_RISK_STATUS_WARNING = '2'    #报警状态
    MID_RISK_STATUS_FORBID = '3'    #风控状态

class CancelTypeEnum(Enum):
    '''None'''
    BILATERAL = '0'    #
    BID_UNILATERAL = '1'    #
    ASK_UNILATERAL = '2'    #

class MidRiskConfigTypeEnum(Enum):
    '''None'''
    INSTRUMENTS_IN_PRODUCT = '1'    #产品下的每个合约
    INSTRUMENTS_IN_OPTION_SERIAL = '2'    #系列下的每个合约
    SPEC_INSTRUMENT = '3'    #合约
    OPTION_SERIAL_IN_PRODUCT = '4'    #产品下每个系列
    SPEC_OPTION_SERIAL = '5'    #系列
    SEPC_PRODUCT = '6'    #产品
    PRODUCTSUM_IN_TRADING_ACCOUNT = '7'    #交易账户下产品总量

class YesOrNoEnum(Enum):
    '''None'''
    YES = '0'    #是
    NO = '1'    #否

class AuthorityCategoryEnum(Enum):
    '''None'''
    READ = '0'    #策略参数只读属性
    READ_WRITE = '1'    #策略参数读写属性

class LastOperateSourceEnum(Enum):
    '''None'''
    TERMINAL = '0'    #客户端
    STRATEGY = '1'    #策略

class CancelRequestOperateTypeEnum(Enum):
    '''None'''
    CANCEL_ORDER = '0'    #撤报单
    CANCEL_QUOTE = '1'    #撤报价

class SystemStatusEnum(Enum):
    '''None'''
    SYSTEM_STATUS_PREPARING = '1'    #系统准备中
    SYSTEM_STATUS_ERROR = '2'    #系统错误
    SYSTEM_STATUS_RUNNING = '3'    #系统运行中

class NodeStatusEnum(Enum):
    '''None'''
    NODE_SUCCESS = '1'    #就绪
    NODE_FAILED = '2'    #失败
    NODE_PREPARING = '3'    #准备中

class TradingAccountStatusEnum(Enum):
    '''None'''
    TRADING_ACCOUNT_STATUS_NORMAL = '0'    #正常交易
    TRADING_ACCOUNT_STATUS_EMERGENCY = '1'    #紧急制动

class EmergencyStatusEnum(Enum):
    '''None'''
    EMERGENCY_START = '1'    #开始执行
    EMERGENCY_COMPLETE = '2'    #执行完成

class SystemSwitchCategoryNameEnum(Enum):
    '''None'''
    SWITCH_UNKNOWN = '0'    #未知
    EMERGENCY_BRAKING = '1'    #系统紧急制动开关
    AUTO_VERIFY = '2'    #自动组合校验开关

class StategyStreamStatusEnum(Enum):
    '''None'''
    STREAM_UNACTIVE = '0'    #取消订阅
    STREAM_ACTIVE = '1'    #订阅消息

class ExchangeUpdateSourceEnum(Enum):
    '''None'''
    UPDATE_BY_UNKNOWN = '0'    #未知
    UPDATE_BY_INIT = '1'    #初始化
    UPDATE_BY_MD = '2'    #行情
    UPDATE_BY_TRADE = '3'    #交易
    UPDATE_BY_CLIENT = '4'    #客户端

class ExecActionEnum(Enum):
    '''None'''
    OPTION_EXEC = '0'    #期权执行
    OPTION_ABANDON = '1'    #放弃执行

class AutocloseFlagEnum(Enum):
    '''None'''
    AUTOCLOSE_FLAG_OFF = '0'    #不对冲
    AUTOCLOSE_FLAG_ON = '1'    #对冲

class ExecStatusEnum(Enum):
    '''None'''
    EXEC_STATUS_NONE = '0'    #未知状态
    EXEC_STATUS_WAITING = '1'    #已申报，待确认
    EXEC_STATUS_IN_BOOK = '2'    #交易所已确认
    EXEC_STATUS_CANCEL = '3'    #已取消
    EXEC_STATUS_ERROR = '4'    #行权申请错误
    EXEC_STATUS_TIMEOUT = '5'    #超时状态

class StrategyPauseReasonEnum(Enum):
    '''None'''
    PAUSE_BY_UNKNOWN = '0'    #未知
    PAUSE_BY_USER_OPERATOR = '1'    #用户手动暂停
    PAUSE_BY_EMERGENCY = '2'    #紧急制动暂停
    PAUSE_BY_STRATEGY_OPERATOR = '3'    #策略主动暂停
    PAUSE_BY_TIMEOUT = '4'    #中后台连接超时暂停

class SceneAnalysisResultEnum(Enum):
    '''None'''
    UNKOWN_TYPE = '0'    #未知
    PNL_TYPE = '1'    #pnl
    DELTA_TYPE = '2'    #delta
    GAMMA_TYPE = '3'    #gamma
    VEGA_TYPE = '4'    #vega
    THETA_TYPE = '5'    #theta

class CreateRedemptActionEnum(Enum):
    '''None'''
    ETF_CREATETION = '0'    #ETF申购
    ETF_REDEMPTION = '1'    #ETF赎回
    INSTANT_ETF_CREATETION = '2'    #ETF一键申购
    INSTANT_ETF_REDEMPTION = '3'    #ETF一键赎回

class BuyRedeemSwitchEnum(Enum):
    '''None'''
    BUY_SWITCH = '0'    #买入风控开关
    REDEEM_SWITCH = '1'    #赎回风控开关
    ALL_SWITCH = '2'    #买入赎回开关

class ActionTypeEnum(Enum):
    '''None'''
    ETF_ACTION_ORDER_BUY = '0'    #基金买入
    ETF_ACTION_ORDER_SELL = '1'    #基金卖出
    ETF_ACTION_CREATETION = '2'    #基金申购
    ETF_ACTION_REDEMPTION = '3'    #基金赎回
    ETF_ACTION_BASKET_BUY = '4'    #篮子买入
    ETF_ACTION_BASKET_SELL = '5'    #篮子卖出
    ETF_ACTION_INSTANT_CREATETION = '6'    #一键申购
    ETF_ACTION_INSTANT_REDEMPTION = '7'    #一键赎回
    ETF_ACTION_CANCEL_INSERT_ORDER = '8'    #手动撤补
    ETF_ACTION_MANUAL_PADDING_ORDER = '9'    #手动补单

class OrderLogLevelEnum(Enum):
    '''None'''
    ORDER_LOG_ALL = '0'    #ALL日志
    ORDER_LOG_INFO = '1'    #INFO日志
    ORDER_LOG_ERROR = '2'    #ERROR日志

class EndHandlerTypeEnum(Enum):
    '''None'''
    END_HANDLER_ORDER_PENDING = '0'    #挂单模式
    END_HANDLER_ORDER_CANCEL = '1'    #撤单模式
    END_HANDLER_ORDER_TOP = '2'    #撤补模式

