'''
CFFEX Confidential.

@Copyright 2018 CFFEX.  All rights reserved.

The source code for this program is not published or otherwise
divested of its trade secrets, irrespective of what has been
deposited with the China Copyright Office.

Author:  Febao
Date:    2025-07-28 10:49:05
Version: 0.0.7
'''

import sys
import datetime
from abc import abstractmethod
from .enum import *
from .models import *
from pyfbapi_core.mm_front import py_front_api

class base_gateway:
    def __init__(self) -> None:
        pass

    @abstractmethod
    def handle_front_connected(self, data: bytearray) -> None:
        pass

    @abstractmethod
    def handle_front_disconnected(self, data: bytearray) -> None:
        pass

    @abstractmethod
    def handle_rsp_info(self, data: RspInfoMsg, request_id: int) -> None:
        pass

    @abstractmethod
    def handle_rtn_market_data(self, data: MarketDataMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_order(self, data: OrderMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_security_instrument(self, data: SecurityInstrumentMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_option_instrument(self, data: OptionInstrumentMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_future_instrument(self, data: FutureInstrumentMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_position(self, data: PositionMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_instrument_param_define(self, data: InstrumentParamDefineMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_instrument_param_value(self, data: InstrumentParamValueMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_custom_param_define(self, data: CustomParamDefineMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_custom_param_value(self, data: CustomParamValueMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_user(self, data: UserMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_user_password(self, data: UserPasswordMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_trading_account(self, data: TradingAccountMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_trader_account_config(self, data: TraderAccountConfigMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_account_product_config(self, data: AccountProductConfigMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_product(self, data: ProductMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_notify_cancel_order(self, data: NotifyCancelOrderMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_create_redempt_order(self, data: CreateRedemptOrderMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_trade(self, data: TradeMsg) -> None:
        pass

    @abstractmethod
    def handle_rtn_investor_account_fund(self, data: InvestorAccountFundMsg) -> None:
        pass


class front_gateway(py_front_api):
    """"""
    def __init__(self, gateway: base_gateway) -> None:
        """构造函数"""
        super().__init__()

        self.gateway = gateway
        self.sub_info: dict[str, list] = {}
        self.sub_info['subscribe'] = []
        self.sub_info['subscribe_instruments'] = []
        self.sub_info['enanble_instrument_filter'] = True
        self.reqid: int = 0
        self.connect_status: bool = False

    def on_handle_front_connected(self, data: bytearray) -> None:
        """连接成功回报"""
        self.gateway.handle_front_connected(data)

    def on_handle_front_disconnected(self, data: bytearray) -> None:
        """连接断开回报"""
        self.gateway.handle_front_disconnected(data)

    def on_handle_rsp_info(self, data: bytearray, request_id: int) -> None:
        """连接断开回报"""
        rtn_data = RspInfoMsg.model_validate_json(data)
        self.gateway.handle_rsp_info(rtn_data, request_id)

    def on_handle_rtn_market_data(self, data: bytearray) -> None:
        rtn_data = MarketDataMsg.model_validate_json(data)
        self.gateway.handle_rtn_market_data(rtn_data)

    def on_handle_rtn_order(self, data: bytearray) -> None:
        rtn_data = OrderMsg.model_validate_json(data)
        self.gateway.handle_rtn_order(rtn_data)

    def on_handle_rtn_security_instrument(self, data: bytearray) -> None:
        rtn_data = SecurityInstrumentMsg.model_validate_json(data)
        self.gateway.handle_rtn_security_instrument(rtn_data)

    def on_handle_rtn_option_instrument(self, data: bytearray) -> None:
        rtn_data = OptionInstrumentMsg.model_validate_json(data)
        self.gateway.handle_rtn_option_instrument(rtn_data)

    def on_handle_rtn_future_instrument(self, data: bytearray) -> None:
        rtn_data = FutureInstrumentMsg.model_validate_json(data)
        self.gateway.handle_rtn_future_instrument(rtn_data)

    def on_handle_rtn_position(self, data: bytearray) -> None:
        rtn_data = PositionMsg.model_validate_json(data)
        self.gateway.handle_rtn_position(rtn_data)

    def on_handle_rtn_instrument_param_define(self, data: bytearray) -> None:
        rtn_data = InstrumentParamDefineMsg.model_validate_json(data)
        self.gateway.handle_rtn_instrument_param_define(rtn_data)

    def on_handle_rtn_instrument_param_value(self, data: bytearray) -> None:
        rtn_data = InstrumentParamValueMsg.model_validate_json(data)
        self.gateway.handle_rtn_instrument_param_value(rtn_data)

    def on_handle_rtn_custom_param_define(self, data: bytearray) -> None:
        rtn_data = CustomParamDefineMsg.model_validate_json(data)
        self.gateway.handle_rtn_custom_param_define(rtn_data)

    def on_handle_rtn_custom_param_value(self, data: bytearray) -> None:
        rtn_data = CustomParamValueMsg.model_validate_json(data)
        self.gateway.handle_rtn_custom_param_value(rtn_data)

    def on_handle_rtn_user(self, data: bytearray) -> None:
        rtn_data = UserMsg.model_validate_json(data)
        self.gateway.handle_rtn_user(rtn_data)

    def on_handle_rtn_user_password(self, data: bytearray) -> None:
        rtn_data = UserPasswordMsg.model_validate_json(data)
        self.gateway.handle_rtn_user_password(rtn_data)

    def on_handle_rtn_trading_account(self, data: bytearray) -> None:
        rtn_data = TradingAccountMsg.model_validate_json(data)
        self.gateway.handle_rtn_trading_account(rtn_data)

    def on_handle_rtn_trader_account_config(self, data: bytearray) -> None:
        rtn_data = TraderAccountConfigMsg.model_validate_json(data)
        self.gateway.handle_rtn_trader_account_config(rtn_data)

    def on_handle_rtn_account_product_config(self, data: bytearray) -> None:
        rtn_data = AccountProductConfigMsg.model_validate_json(data)
        self.gateway.handle_rtn_account_product_config(rtn_data)

    def on_handle_rtn_product(self, data: bytearray) -> None:
        rtn_data = ProductMsg.model_validate_json(data)
        self.gateway.handle_rtn_product(rtn_data)

    def on_handle_rtn_notify_cancel_order(self, data: bytearray) -> None:
        rtn_data = NotifyCancelOrderMsg.model_validate_json(data)
        self.gateway.handle_rtn_notify_cancel_order(rtn_data)

    def on_handle_rtn_create_redempt_order(self, data: bytearray) -> None:
        rtn_data = CreateRedemptOrderMsg.model_validate_json(data)
        self.gateway.handle_rtn_create_redempt_order(rtn_data)

    def on_handle_rtn_trade(self, data: bytearray) -> None:
        rtn_data = TradeMsg.model_validate_json(data)
        self.gateway.handle_rtn_trade(rtn_data)

    def on_handle_rtn_investor_account_fund(self, data: bytearray) -> None:
        rtn_data = InvestorAccountFundMsg.model_validate_json(data)
        self.gateway.handle_rtn_investor_account_fund(rtn_data)

    def register_table(self, table_name: str, table_interval: int) -> None:
        self.sub_info['subscribe'].append({'table': table_name, 'interval': table_interval})

    def register_instruments(self, instruments: list[str]) -> None:
        self.sub_info['subscribe_instruments'].extend(instruments)

    def connect(self, pub_address: str, req_address: str, log_level: str, log_dir: str) -> None:
        """连接后台,log_level: all|trace|debug|info|warning|error|fatal"""
        super().subscribe(self.sub_info)
        super().init(pub_address, req_address, log_level, log_dir)
        super().start()

    def join(self) -> None:
        super().join()

    def close(self) -> None:
        super().exit()

    def do_insert_order(self, req: OrderEntity, reqid: int) -> None:
        super().insert_order(req.dict(), reqid)

    def do_cancel_order(self, req: OrderEntity, reqid: int) -> None:
        super().cancel_order(req.dict(), reqid)

    def do_modify_instrument_param_value(self, req: InstrumentParamValueEntity, reqid: int) -> None:
        super().modify_instrument_param_value(req.dict(), reqid)

    def do_modify_custom_param_value(self, req: CustomParamValueEntity, reqid: int) -> None:
        super().modify_custom_param_value(req.dict(), reqid)

