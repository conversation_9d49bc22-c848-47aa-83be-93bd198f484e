#include <cfloat>
#include <cstddef>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <stdexcept>
#include <pybind11/pybind11.h>

using namespace pybind11;
using namespace std;

template <typename TASK>
class task_factory {
public:
    static void destroy(TASK *p) {
        p->~TASK();
        ::free(p);
    }

    static TASK *create() {
        TASK *p = (TASK *)::malloc(sizeof(TASK));
        ::new(p) TASK();
        return p;
    }

    void operator delete(void *) {
        throw std::runtime_error("forbidden delete task_factory");
    }
private:
    void *operator new(std::size_t);
};

// Task
struct Task : public task_factory<Task>
{
    int task_name;    // 回调函数对应名称（值）
    void *task_data;  // 数据指针
    int data_len;     // 数据长度
    void *task_error; // 错误指针
    int task_id;      // task_id
    bool task_last;   // 是否为最后返回

    Task() : task_data(nullptr), data_len(0) { }
};

class TerminatedError : std::exception
{
};

class TaskQueue
{
private:
    queue<Task*> queue_;       //标准库队列
    mutex mutex_;             //互斥锁
    condition_variable cond_; //条件变量

    bool _terminate = false;

public:
    //存入新的任务
    void push(Task *task)
    {
        unique_lock<mutex> mlock(mutex_);
        queue_.push(task);  //向队列中存入数据
        mlock.unlock();     //释放锁
        cond_.notify_one(); //通知正在阻塞等待的线程
    }

    //取出老的任务
    Task *pop()
    {
        unique_lock<mutex> mlock(mutex_);
        cond_.wait(mlock, [&]()
                   { return !queue_.empty() || _terminate; }); //等待条件变量通知
        if (_terminate)
            return nullptr;
        Task *task = queue_.front(); //获取队列中的最后一个任务
        queue_.pop();               //删除该任务
        return task;                //返回该任务
    }

    void terminate()
    {
        _terminate = true;
        cond_.notify_all(); //通知正在阻塞等待的线程
    }
};


//从字典中获取某个建值对应的整数，并赋值到请求结构体对象的值上
void getInt(const dict &d, const char *key, int *value)
{
    if (d.contains(key))		//检查字典中是否存在该键值
    {
        object o = d[key];		//获取该键值
        *value = o.cast<int>();
    }
};

int get_int(const dict &d, const char *key) {
    if (d.contains(key)) {
        object o = d[key];
        return o.cast<int>();
    }
    return 0;
}

//从字典中获取某个建值对应的浮点数，并赋值到请求结构体对象的值上
void getDouble(const dict &d, const char *key, double *value)
{
    if (d.contains(key))
    {
        object o = d[key];
        *value = o.cast<double>();
    }
};

double get_double(const dict &d, const char *key) {
    if (d.contains(key)) {
        object o = d[key];
        return o.cast<double>();
    }
    return DBL_MAX;
}


//从字典中获取某个建值对应的字符，并赋值到请求结构体对象的值上
void getChar(const dict &d, const char *key, char *value)
{
    if (d.contains(key))
    {
        object o = d[key];
        *value = o.cast<char>();
    }
};

char get_char(const dict &d, const char *key) {
    if (d.contains(key)) {
        object o = d[key];
        return o.cast<char>();
    }
    return 0;
}


template <size_t size>
using string_literal = char[size];

//从字典中获取某个建值对应的字符串，并赋值到请求结构体对象的值上
template <size_t size>
void getString(const pybind11::dict &d, const char *key, string_literal<size> &value)
{
    if (d.contains(key))
    {
        object o = d[key];
        string s = o.cast<string>();
        const char *buf = s.c_str();
        strcpy(value, buf);
    }
};

const string get_str(const dict &d, const char *key) {
    if (d.contains(key)) {
        object o = d[key];
        string s = o.cast<string>();
        return s;
    }
    return "";
}

pybind11::list get_list(const dict &d, const char *key) {
    if (d.contains(key)) {
        object o = d[key];
        pybind11::list l = o.cast<pybind11::list>();
        return l;
    }
    pybind11::list empty_l;
    return empty_l;
}

bool get_bool(const dict &d, const char *key) {
    if (d.contains(key)) {
        object o = d[key];
        try {
            // 直接尝试转换为bool
            bool b = o.cast<bool>();
            return b;
        } catch (const pybind11::cast_error&) {
            // 如果转换失败，返回false作为默认值
            return false;
        }
    }
    return false;
}

