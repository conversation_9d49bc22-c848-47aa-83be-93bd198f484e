#include "py_front_api.h"
#include <pybind11/pybind11.h>
#include <iostream>
#include <map>
#include <stdlib.h>

using namespace cffex::mm::front_api;


void py_front_api::subscribe(const pybind11::dict &req) {
    sub_info_ = mm_front_api::subscribe_info::create_subscribe_info();
    pybind11::list l = get_list(req, "subscribe");
    for (pybind11::detail::list_iterator itor = l.begin(); itor != l.end(); ++itor) {
        pybind11::dict d = itor->cast<pybind11::dict>();
        sub_info_->append(get_str(d, "table").c_str(), get_int(d, "interval"));
    }

    pybind11::list instrument_list = get_list(req, "subscribe_instruments");
    for (pybind11::detail::list_iterator itor = instrument_list.begin(); itor != instrument_list.end(); ++itor) {
        std::string instrument_id = itor->cast<std::string>();
        sub_info_->append_instrument(instrument_id.c_str());
    }

    bool enable_filter = false;
    if (req.contains("enable_instrument_filter")) {
        enable_filter = get_bool(req, "enable_instrument_filter");
    }
    sub_info_->set_enable_instrument_filter(enable_filter);
}
int py_front_api::init(const char *pub_addr, const char *req_addr, const char *log_level, const char *log_dir) {
    is_active_ = true;
    api_       = mm_front_api::create_mm_front_api(1, log_level, log_dir);
    api_->register_spi(this);
    thread_ = std::thread(&py_front_api::process_task, this);
    api_->init(pub_addr, req_addr, sub_info_);
    return 0;
}
void py_front_api::start() {
    api_->start();
}
void py_front_api::release() {
    api_->release();
}
int py_front_api::join() {
    return api_->join();
}
int py_front_api::exit() {
    is_active_ = false;
    task_queue_.terminate();
    thread_.join();
    api_->register_spi(nullptr);
    api_->release();
    api_ = nullptr;
    return 1;
}

void py_front_api::on_front_connected(const char* ip) {
    Task *task      = Task::create();
    task->task_name = ON_FRONT_CONNECTED;
    task->data_len = strlen(ip);
    task->task_data = ::malloc(task->data_len);
    memcpy(task->task_data, ip, task->data_len);
    task_queue_.push(task);
}
void py_front_api::process_front_connected(Task *task) {
    gil_scoped_acquire acquire;
    this->on_handle_front_connected(pybind11::bytearray((const char *)task->task_data, task->data_len));
    task->destroy(task);
}
void py_front_api::on_front_disconnected(const char* ip) {
    Task *task      = Task::create();
    task->task_name = ON_FRONT_DISCONNECTED;
    task->data_len = strlen(ip);
    task->task_data = ::malloc(task->data_len);
    memcpy(task->task_data, ip, task->data_len);
    task_queue_.push(task);
}
void py_front_api::process_front_disconnected(Task *task) {
    gil_scoped_acquire acquire;
    this->on_handle_front_disconnected(pybind11::bytearray((const char *)task->task_data, task->data_len));
    task->destroy(task);
}
void py_front_api::on_rsp_info(rsp_info_msg *rsp_info, int request_id) {
    Task *task      = Task::create();
    task->task_name = ON_RSP_INFO;
    if (rsp_info) {
        static char sz[10240] = { 0 };
        task->data_len = rsp_info->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task->task_id = request_id;
    task->task_last = true;
    task_queue_.push(task);
}
void py_front_api::process_rsp_info(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rsp_info(pybind11::bytearray((const char *)task->task_data, task->data_len), task->task_id);
        ::free(task->task_data);
        task->destroy(task);
    }
}
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////

void py_front_api::on_rtn_market_data(market_data_msg *market_data) {

    auto start_time = std::chrono::high_resolution_clock::now();

    Task *task      = Task::create();
    task->task_name = ON_RTN_MARKET_DATA;

    if (market_data) {
        static char sz[10240] = { 0 };
        task->data_len = market_data->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
    
    on_rtn_md_data_total_time_ += duration;
    uint32_t  count = ++on_rtn_md_count_;

    if (count % 100 == 0) {
        double avg_time_ns = static_cast<double>(on_rtn_md_data_total_time_.load()) / count;
        double avg_time_us = avg_time_ns / 1000.0;
        std::cout << "[PERF] on_rtn_market_data: count=" << count
                  << ", avg_time=" << avg_time_us << "us" << std::endl;
    }

}
void py_front_api::process_rtn_market_data(Task *task) {


    auto start_time = std::chrono::high_resolution_clock::now();

    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_market_data(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
    
    process_md_data_total_time_ += duration;
    uint32_t  count = ++process_md_count_;

    if (count % 100 == 0) {
        double avg_time_ns = static_cast<double>(process_md_data_total_time_.load()) / count;
        double avg_time_us = avg_time_ns / 1000.0;
        std::cout << "[PERF] process_rtn_market_data: count=" << count
                  << ", avg_time=" << avg_time_us << "us" << std::endl;
    }
}

void py_front_api::on_rtn_order(order_msg *order) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_ORDER;
    if (order) {
        static char sz[10240] = { 0 };
        task->data_len = order->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_order(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_order(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_security_instrument(security_instrument_msg *security_instrument) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_SECURITY_INSTRUMENT;
    if (security_instrument) {
        static char sz[10240] = { 0 };
        task->data_len = security_instrument->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_security_instrument(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_security_instrument(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_option_instrument(option_instrument_msg *option_instrument) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_OPTION_INSTRUMENT;
    if (option_instrument) {
        static char sz[10240] = { 0 };
        task->data_len = option_instrument->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_option_instrument(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_option_instrument(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_future_instrument(future_instrument_msg *future_instrument) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_FUTURE_INSTRUMENT;
    if (future_instrument) {
        static char sz[10240] = { 0 };
        task->data_len = future_instrument->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_future_instrument(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_future_instrument(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_position(position_msg *position) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_POSITION;
    if (position) {
        static char sz[10240] = { 0 };
        task->data_len = position->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_position(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_position(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_instrument_param_define(instrument_param_define_msg *instrument_param_define) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_INSTRUMENT_PARAM_DEFINE;
    if (instrument_param_define) {
        static char sz[10240] = { 0 };
        task->data_len = instrument_param_define->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_instrument_param_define(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_instrument_param_define(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_instrument_param_value(instrument_param_value_msg *instrument_param_value) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_INSTRUMENT_PARAM_VALUE;
    if (instrument_param_value) {
        static char sz[10240] = { 0 };
        task->data_len = instrument_param_value->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_instrument_param_value(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_instrument_param_value(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_custom_param_define(custom_param_define_msg *custom_param_define) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_CUSTOM_PARAM_DEFINE;
    if (custom_param_define) {
        static char sz[10240] = { 0 };
        task->data_len = custom_param_define->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_custom_param_define(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_custom_param_define(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_custom_param_value(custom_param_value_msg *custom_param_value) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_CUSTOM_PARAM_VALUE;
    if (custom_param_value) {
        static char sz[10240] = { 0 };
        task->data_len = custom_param_value->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_custom_param_value(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_custom_param_value(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_user(user_msg *user) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_USER;
    if (user) {
        static char sz[10240] = { 0 };
        task->data_len = user->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_user(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_user(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_user_password(user_password_msg *user_password) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_USER_PASSWORD;
    if (user_password) {
        static char sz[10240] = { 0 };
        task->data_len = user_password->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_user_password(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_user_password(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_trading_account(trading_account_msg *trading_account) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_TRADING_ACCOUNT;
    if (trading_account) {
        static char sz[10240] = { 0 };
        task->data_len = trading_account->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_trading_account(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_trading_account(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_trader_account_config(trader_account_config_msg *trader_account_config) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_TRADER_ACCOUNT_CONFIG;
    if (trader_account_config) {
        static char sz[10240] = { 0 };
        task->data_len = trader_account_config->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_trader_account_config(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_trader_account_config(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_account_product_config(account_product_config_msg *account_product_config) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_ACCOUNT_PRODUCT_CONFIG;
    if (account_product_config) {
        static char sz[10240] = { 0 };
        task->data_len = account_product_config->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_account_product_config(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_account_product_config(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_product(product_msg *product) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_PRODUCT;
    if (product) {
        static char sz[10240] = { 0 };
        task->data_len = product->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_product(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_product(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_notify_cancel_order(notify_cancel_order_msg *notify_cancel_order) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_NOTIFY_CANCEL_ORDER;
    if (notify_cancel_order) {
        static char sz[10240] = { 0 };
        task->data_len = notify_cancel_order->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_notify_cancel_order(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_notify_cancel_order(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_create_redempt_order(create_redempt_order_msg *create_redempt_order) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_CREATE_REDEMPT_ORDER;
    if (create_redempt_order) {
        static char sz[10240] = { 0 };
        task->data_len = create_redempt_order->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_create_redempt_order(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_create_redempt_order(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_trade(trade_msg *trade) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_TRADE;
    if (trade) {
        static char sz[10240] = { 0 };
        task->data_len = trade->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_trade(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_trade(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

void py_front_api::on_rtn_investor_account_fund(investor_account_fund_msg *investor_account_fund) {
    Task *task      = Task::create();
    task->task_name = ON_RTN_INVESTOR_ACCOUNT_FUND;
    if (investor_account_fund) {
        static char sz[10240] = { 0 };
        task->data_len = investor_account_fund->to_json(sz, 10240);
        task->task_data = ::malloc(task->data_len);
        memcpy(task->task_data, sz, task->data_len);
    }
    task_queue_.push(task);
}
void py_front_api::process_rtn_investor_account_fund(Task *task) {
    gil_scoped_acquire acquire;
    if (task->task_data) {
        this->on_handle_rtn_investor_account_fund(pybind11::bytearray((const char *)task->task_data, task->data_len));
        ::free(task->task_data);
        task->destroy(task);
    }
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void py_front_api::process_task() {
    while (is_active_) {
        Task *task = task_queue_.pop();
        if(task == nullptr) break;
        switch (task->task_name) {
            case ON_FRONT_CONNECTED: {
                process_front_connected(task);
                break;
            }
            case ON_FRONT_DISCONNECTED: {
                process_front_disconnected(task);
                break;
            }
            case ON_RSP_INFO: {
                process_rsp_info(task);
                break;
            }
            case ON_RTN_MARKET_DATA: {
                process_rtn_market_data(task);
                break;
            }
            case ON_RTN_ORDER: {
                process_rtn_order(task);
                break;
            }
            case ON_RTN_SECURITY_INSTRUMENT: {
                process_rtn_security_instrument(task);
                break;
            }
            case ON_RTN_OPTION_INSTRUMENT: {
                process_rtn_option_instrument(task);
                break;
            }
            case ON_RTN_FUTURE_INSTRUMENT: {
                process_rtn_future_instrument(task);
                break;
            }
            case ON_RTN_POSITION: {
                process_rtn_position(task);
                break;
            }
            case ON_RTN_INSTRUMENT_PARAM_DEFINE: {
                process_rtn_instrument_param_define(task);
                break;
            }
            case ON_RTN_INSTRUMENT_PARAM_VALUE: {
                process_rtn_instrument_param_value(task);
                break;
            }
            case ON_RTN_CUSTOM_PARAM_DEFINE: {
                process_rtn_custom_param_define(task);
                break;
            }
            case ON_RTN_CUSTOM_PARAM_VALUE: {
                process_rtn_custom_param_value(task);
                break;
            }
            case ON_RTN_USER: {
                process_rtn_user(task);
                break;
            }
            case ON_RTN_USER_PASSWORD: {
                process_rtn_user_password(task);
                break;
            }
            case ON_RTN_TRADING_ACCOUNT: {
                process_rtn_trading_account(task);
                break;
            }
            case ON_RTN_TRADER_ACCOUNT_CONFIG: {
                process_rtn_trader_account_config(task);
                break;
            }
            case ON_RTN_ACCOUNT_PRODUCT_CONFIG: {
                process_rtn_account_product_config(task);
                break;
            }
            case ON_RTN_PRODUCT: {
                process_rtn_product(task);
                break;
            }
            case ON_RTN_NOTIFY_CANCEL_ORDER: {
                process_rtn_notify_cancel_order(task);
                break;
            }
            case ON_RTN_CREATE_REDEMPT_ORDER: {
                process_rtn_create_redempt_order(task);
                break;
            }
            case ON_RTN_TRADE: {
                process_rtn_trade(task);
                break;
            }
            case ON_RTN_INVESTOR_ACCOUNT_FUND: {
                process_rtn_investor_account_fund(task);
                break;
            }
        }
    }
}
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
int  py_front_api::insert_order(const pybind11::dict &req, int reqid) {
    order_entity *entity = api_->get_entity_creator()->create_order_entity();
    entity->set_instrument_id(get_str(req, "instrument_id").c_str());
    entity->set_direction(get_char(req, "direction"));
    entity->set_offset_flag(get_char(req, "offset_flag"));
    entity->set_hedge_flag(get_char(req, "hedge_flag"));
    entity->set_price(get_double(req, "price"));
    entity->set_volume(get_int(req, "volume"));
    entity->set_price_category(get_char(req, "price_category"));
    entity->set_time_condition(get_char(req, "time_condition"));
    entity->set_volume_condition(get_char(req, "volume_condition"));
    entity->set_portfolio_id(get_int(req, "portfolio_id"));
    entity->set_order_id(get_int64_t(req, "order_id"));
    entity->set_priority(get_int(req, "priority"));
    entity->set_custom_flag(get_str(req, "custom_flag").c_str());
    entity->set_seat_no(get_str(req, "seat_no").c_str());
    entity->set_trading_account_id(get_int(req, "trading_account_id"));
    entity->set_investor_id(get_str(req, "investor_id").c_str());
    entity->set_order_source(get_char(req, "order_source"));
    return api_->insert_order(entity, reqid);
}
int  py_front_api::cancel_order(const pybind11::dict &req, int reqid) {
    order_entity *entity = api_->get_entity_creator()->create_order_entity();
    entity->set_instrument_id(get_str(req, "instrument_id").c_str());
    entity->set_direction(get_char(req, "direction"));
    entity->set_offset_flag(get_char(req, "offset_flag"));
    entity->set_hedge_flag(get_char(req, "hedge_flag"));
    entity->set_price(get_double(req, "price"));
    entity->set_volume(get_int(req, "volume"));
    entity->set_price_category(get_char(req, "price_category"));
    entity->set_time_condition(get_char(req, "time_condition"));
    entity->set_volume_condition(get_char(req, "volume_condition"));
    entity->set_portfolio_id(get_int(req, "portfolio_id"));
    entity->set_order_id(get_int64_t(req, "order_id"));
    entity->set_priority(get_int(req, "priority"));
    entity->set_custom_flag(get_str(req, "custom_flag").c_str());
    entity->set_seat_no(get_str(req, "seat_no").c_str());
    entity->set_trading_account_id(get_int(req, "trading_account_id"));
    entity->set_investor_id(get_str(req, "investor_id").c_str());
    entity->set_order_source(get_char(req, "order_source"));
    return api_->cancel_order(entity, reqid);
}
int  py_front_api::modify_instrument_param_value(const pybind11::dict &req, int reqid) {
    instrument_param_value_entity *entity = api_->get_entity_creator()->create_instrument_param_value_entity();
    entity->set_instrument_id(get_str(req, "instrument_id").c_str());
    entity->set_trading_account_id(get_int(req, "trading_account_id"));
    entity->set_param_key(get_str(req, "param_key").c_str());
    entity->set_param_value(get_str(req, "param_value").c_str());
    entity->set_last_operator_id(get_int(req, "last_operator_id"));
    entity->set_last_operate_source(get_char(req, "last_operate_source"));
    entity->set_status(get_char(req, "status"));
    return api_->modify_instrument_param_value(entity, reqid);
}
int  py_front_api::modify_custom_param_value(const pybind11::dict &req, int reqid) {
    custom_param_value_entity *entity = api_->get_entity_creator()->create_custom_param_value_entity();
    entity->set_custom_id(get_str(req, "custom_id").c_str());
    entity->set_trading_account_id(get_int(req, "trading_account_id"));
    entity->set_param_key(get_str(req, "param_key").c_str());
    entity->set_param_value(get_str(req, "param_value").c_str());
    entity->set_last_operator_id(get_int(req, "last_operator_id"));
    entity->set_last_operate_source(get_char(req, "last_operate_source"));
    entity->set_status(get_char(req, "status"));
    return api_->modify_custom_param_value(entity, reqid);
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
class py_api : public py_front_api {
public:
    using py_front_api::py_front_api;

    void on_handle_front_connected(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_front_connected, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    };
    void on_handle_front_disconnected(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_front_disconnected, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rsp_info(const pybind11::bytearray &data, int reqid) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rsp_info, data, reqid);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }


    void on_handle_rtn_market_data(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_market_data, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_order(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_order, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_security_instrument(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_security_instrument, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_option_instrument(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_option_instrument, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_future_instrument(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_future_instrument, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_position(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_position, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_instrument_param_define(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_instrument_param_define, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_instrument_param_value(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_instrument_param_value, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_custom_param_define(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_custom_param_define, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_custom_param_value(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_custom_param_value, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_user(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_user, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_user_password(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_user_password, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_trading_account(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_trading_account, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_trader_account_config(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_trader_account_config, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_account_product_config(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_account_product_config, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_product(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_product, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_notify_cancel_order(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_notify_cancel_order, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_create_redempt_order(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_create_redempt_order, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_trade(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_trade, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }
    void on_handle_rtn_investor_account_fund(const pybind11::bytearray &data) override {
        try {
            PYBIND11_OVERLOAD(void, py_front_api, on_handle_rtn_investor_account_fund, data);
        } catch (const pybind11::error_already_set &e) { cout << e.what() << endl; }
    }

};

void init_front_api(module &m) {
    class_<py_front_api, py_api>(m, "py_front_api")
        .def(init<>())
        .def("init",      &py_front_api::init)
        .def("start",     &py_front_api::start)
        .def("release",   &py_front_api::release)
        .def("join",      &py_front_api::join)
        .def("exit",      &py_front_api::exit)
        .def("subscribe", &py_front_api::subscribe)
        .def("on_handle_front_connected", &py_front_api::on_handle_front_connected)
        .def("on_handle_front_disconnected", &py_front_api::on_handle_front_disconnected)
        .def("on_handle_rsp_info", &py_front_api::on_handle_rsp_info)
        .def("insert_order", &py_front_api::insert_order)
        .def("cancel_order", &py_front_api::cancel_order)
        .def("modify_instrument_param_value", &py_front_api::modify_instrument_param_value)
        .def("modify_custom_param_value", &py_front_api::modify_custom_param_value)
        .def("on_handle_rtn_market_data", &py_front_api::on_handle_rtn_market_data)
        .def("on_handle_rtn_order", &py_front_api::on_handle_rtn_order)
        .def("on_handle_rtn_security_instrument", &py_front_api::on_handle_rtn_security_instrument)
        .def("on_handle_rtn_option_instrument", &py_front_api::on_handle_rtn_option_instrument)
        .def("on_handle_rtn_future_instrument", &py_front_api::on_handle_rtn_future_instrument)
        .def("on_handle_rtn_position", &py_front_api::on_handle_rtn_position)
        .def("on_handle_rtn_instrument_param_define", &py_front_api::on_handle_rtn_instrument_param_define)
        .def("on_handle_rtn_instrument_param_value", &py_front_api::on_handle_rtn_instrument_param_value)
        .def("on_handle_rtn_custom_param_define", &py_front_api::on_handle_rtn_custom_param_define)
        .def("on_handle_rtn_custom_param_value", &py_front_api::on_handle_rtn_custom_param_value)
        .def("on_handle_rtn_user", &py_front_api::on_handle_rtn_user)
        .def("on_handle_rtn_user_password", &py_front_api::on_handle_rtn_user_password)
        .def("on_handle_rtn_trading_account", &py_front_api::on_handle_rtn_trading_account)
        .def("on_handle_rtn_trader_account_config", &py_front_api::on_handle_rtn_trader_account_config)
        .def("on_handle_rtn_account_product_config", &py_front_api::on_handle_rtn_account_product_config)
        .def("on_handle_rtn_product", &py_front_api::on_handle_rtn_product)
        .def("on_handle_rtn_notify_cancel_order", &py_front_api::on_handle_rtn_notify_cancel_order)
        .def("on_handle_rtn_create_redempt_order", &py_front_api::on_handle_rtn_create_redempt_order)
        .def("on_handle_rtn_trade", &py_front_api::on_handle_rtn_trade)
        .def("on_handle_rtn_investor_account_fund", &py_front_api::on_handle_rtn_investor_account_fund);
}
